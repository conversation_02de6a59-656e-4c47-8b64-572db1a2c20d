import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  IconButton,
  Tooltip,
  Button,
  TextField,
  Grid,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { Download, History as HistoryIcon, Refresh, Assignment, BugReport, FilterList, ExpandMore, ExpandLess } from '@mui/icons-material';
import ChangelogModal from './ChangelogModal';
import LogsModal from './LogsModal';

export interface HistoryItem {
  id: string;
  fileName: string;
  uploadDate: Date | string;
  fileSize?: string;
  pageCount?: number;
  excelPath?: string;
  isSystemHistory?: boolean;
  processingType?: string;
  processingName?: string;
}

interface HistoryPanelProps {
  history: HistoryItem[];
  onSelectItem: (item: HistoryItem) => void;
  onDownloadItem: (item: HistoryItem) => void;
  onRefresh?: () => void;
  selectedItemId?: string;
  fileDetails?: {
    inputFileName: string;
    inputFileSize: string;
    processDateTime: string;
    status: string;
    processingType?: string;
    processingName?: string;
  } | null;
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({
  history,
  onSelectItem,
  onDownloadItem,
  onRefresh,
  selectedItemId,
  fileDetails
}) => {
  const [changelogOpen, setChangelogOpen] = useState(false);
  const [logsOpen, setLogsOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [dateFilters, setDateFilters] = useState({
    startDate: '',
    endDate: ''
  });
  const [processingFilters, setProcessingFilters] = useState({
    processingType: '',
    processingName: ''
  });

  // Filter history based on date range and processing filters
  const filteredHistory = useMemo(() => {
    const hasDateFilters = dateFilters.startDate || dateFilters.endDate;
    const hasProcessingFilters = processingFilters.processingType || processingFilters.processingName;

    if (!hasDateFilters && !hasProcessingFilters) {
      return history;
    }

    return history.filter(item => {
      // Date filtering
      if (hasDateFilters) {
        const itemDate = new Date(item.uploadDate);
        const itemDateStr = itemDate.toISOString().split('T')[0]; // YYYY-MM-DD format

        if (dateFilters.startDate && itemDateStr < dateFilters.startDate) {
          return false;
        }
        if (dateFilters.endDate && itemDateStr > dateFilters.endDate) {
          return false;
        }
      }

      // Processing type filtering
      if (processingFilters.processingType &&
          item.processingType !== processingFilters.processingType) {
        return false;
      }

      // Processing name filtering
      if (processingFilters.processingName &&
          item.processingName !== processingFilters.processingName) {
        return false;
      }

      return true;
    });
  }, [history, dateFilters, processingFilters]);

  // Get unique processing types and names for filter dropdowns
  const uniqueProcessingTypes = useMemo(() => {
    const types = new Set<string>();
    history.forEach(item => {
      if (item.processingType) {
        types.add(item.processingType);
      }
    });
    return Array.from(types).sort();
  }, [history]);

  const uniqueProcessingNames = useMemo(() => {
    const names = new Set<string>();
    history.forEach(item => {
      if (item.processingName && (!processingFilters.processingType || item.processingType === processingFilters.processingType)) {
        names.add(item.processingName);
      }
    });
    return Array.from(names).sort();
  }, [history, processingFilters.processingType]);

  // Handle date filter changes
  const handleDateFilterChange = (field: 'startDate' | 'endDate', value: string) => {
    setDateFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle processing filter changes
  const handleProcessingFilterChange = (field: 'processingType' | 'processingName', value: string) => {
    setProcessingFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setDateFilters({
      startDate: '',
      endDate: ''
    });
    setProcessingFilters({
      processingType: '',
      processingName: ''
    });
  };

  // Format date to a readable string
  const formatDate = (date: Date | string): string => {
    if (typeof date === 'string') {
      // If it's an ISO string, convert to Date first
      try {
        return new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
          hour: 'numeric',
          minute: 'numeric'
        }).format(new Date(date));
      } catch (e) {
        return date; // If conversion fails, return the string as is
      }
    }

    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        flex: 1,
        borderRadius: 0,
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '6px',
          background: (theme) =>
            theme.palette.mode === 'light'
              ? `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          zIndex: 1
        }
      }}
    >
      <Box sx={{
        p: 3,
        borderBottom: 1,
        borderColor: 'divider',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: (theme) =>
          theme.palette.mode === 'light'
            ? 'rgba(0, 0, 0, 0.02)'
            : 'rgba(255, 255, 255, 0.02)',
        position: 'relative',
        zIndex: 2
      }}>
        <Typography
          variant="subtitle1"
          component="h3"
          sx={{
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center'
          }}
        >
          Upload History
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: (theme) =>
              theme.palette.mode === 'light'
                ? 'rgba(10, 75, 148, 0.08)'
                : 'rgba(33, 150, 243, 0.16)',
            borderRadius: '12px',
            px: 1.5,
            py: 0.5
          }}
        >
          {/* Filter Toggle Button */}
          <Tooltip title={showFilters ? "Hide Filters" : "Show Filters"}>
            <IconButton
              onClick={() => setShowFilters(!showFilters)}
              size="small"
              sx={{
                color: 'primary.main',
                mr: 1,
                '&:hover': {
                  backgroundColor: 'rgba(25, 118, 210, 0.08)'
                }
              }}
            >
              {showFilters ? <ExpandLess fontSize="small" /> : <FilterList fontSize="small" />}
            </IconButton>
          </Tooltip>

          {onRefresh && (
            <Tooltip title="Refresh History">
              <IconButton
                onClick={onRefresh}
                size="small"
                sx={{
                  color: 'primary.main',
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.08)',
                    transform: 'rotate(180deg)',
                    transition: 'transform 0.3s ease'
                  }
                }}
              >
                <Refresh fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          <Typography
            variant="caption"
            sx={{
              color: 'primary.main',
              fontWeight: 600
            }}
          >
            {filteredHistory.filter(item => item.isSystemHistory).length} {filteredHistory.filter(item => item.isSystemHistory).length === 1 ? 'file' : 'files'}
          </Typography>
        </Box>
      </Box>

      {/* Date Filter Controls */}
      <Collapse in={showFilters}>
        <Box sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          bgcolor: (theme) =>
            theme.palette.mode === 'light'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)'
        }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Date Filters Row */}
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
              <TextField
                label="Start Date"
                type="date"
                value={dateFilters.startDate}
                onChange={(e) => handleDateFilterChange('startDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                fullWidth
                size="small"
              />
              <TextField
                label="End Date"
                type="date"
                value={dateFilters.endDate}
                onChange={(e) => handleDateFilterChange('endDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                fullWidth
                size="small"
              />
            </Box>

            {/* Processing Filters Row */}
            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Processing Type</InputLabel>
                <Select
                  value={processingFilters.processingType}
                  label="Processing Type"
                  onChange={(e) => handleProcessingFilterChange('processingType', e.target.value)}
                >
                  <MenuItem value="">All Types</MenuItem>
                  {uniqueProcessingTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth size="small">
                <InputLabel>Processing Name</InputLabel>
                <Select
                  value={processingFilters.processingName}
                  label="Processing Name"
                  onChange={(e) => handleProcessingFilterChange('processingName', e.target.value)}
                >
                  <MenuItem value="">All Names</MenuItem>
                  {uniqueProcessingNames.map((name) => (
                    <MenuItem key={name} value={name}>
                      {name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* Clear Button */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={clearAllFilters}
                sx={{ minWidth: '100px' }}
              >
                Clear All
              </Button>
            </Box>
          </Box>
        </Box>
      </Collapse>

      <Box
        className="scrollable-container"
        sx={{
          flex: 1,
          maxHeight: showFilters
            ? 'calc(100vh - 420px)' // Reduced height when filters are shown (increased for processing filters)
            : 'calc(100vh - 170px)', // Normal height when filters are hidden
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '-ms-overflow-style': 'none', // IE and Edge
          'scrollbarWidth': 'none',     // Firefox
          '&::-webkit-scrollbar-track': {
            background: theme => theme.palette.mode === 'light' ? '#f1f1f1' : '#1e1e1e',
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme => theme.palette.mode === 'light' ? '#c1c1c1' : '#555',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: theme => theme.palette.mode === 'light' ? '#a1a1a1' : '#777',
          }
        }}>
        {filteredHistory.filter(item => item.isSystemHistory).length === 0 ? (
          <Box sx={{
            p: 3,
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            minHeight: '200px',
            animation: 'fadeIn 0.5s ease-in-out',
            borderRadius: 2,
            mx: 2,
            my: 2,
            backgroundColor: (theme) =>
              theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.01)'
                : 'rgba(255, 255, 255, 0.01)'
          }}>
            <Box
              sx={{
                mb: 3,
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  width: '100%',
                  height: '100%',
                  top: 0,
                  left: 0,
                  background: (theme) =>
                    `radial-gradient(circle, ${theme.palette.primary.main}20 0%, transparent 70%)`,
                  borderRadius: '50%',
                  animation: 'pulse 3s infinite ease-in-out',
                  zIndex: -1
                }
              }}
            >
              <HistoryIcon
                sx={{
                  fontSize: 60,
                  color: 'primary.main',
                  opacity: 0.6,
                  filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.1))'
                }}
              />
            </Box>
            <Typography
              variant="h6"
              color="text.primary"
              sx={{ fontWeight: 600, mb: 1 }}
            >
              No System History
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                maxWidth: '240px',
                mb: 3
              }}
            >
              Process files through the system to see them here
            </Typography>
            <Box
              sx={{
                width: '60%',
                height: '4px',
                borderRadius: '2px',
                background: (theme) =>
                  `linear-gradient(90deg, ${theme.palette.primary.main}40, ${theme.palette.secondary.main}40)`,
                position: 'relative',
                overflow: 'hidden',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                  animation: 'shimmer 2s infinite linear'
                }
              }}
            />
          </Box>
        ) : (
          <List
            sx={{
              p: 0,
              '& > .MuiListItem-root:hover': {
                backgroundColor: (theme) =>
                  theme.palette.mode === 'light'
                    ? 'rgba(0, 0, 0, 0.03)'
                    : 'rgba(255, 255, 255, 0.03)'
              }
            }}
          >
            {filteredHistory.filter(item => item.isSystemHistory).map((item, index) => (
              <React.Fragment key={item.id}>
                <ListItem
                  disablePadding
                  sx={{
                    animation: 'fadeIn 0.3s ease-in-out',
                    animationDelay: `${index * 0.05}s`,
                    opacity: 0,
                    animationFillMode: 'forwards',
                    mt: index === 0 ? 1 : 0 // Add margin to the first item
                  }}
                  secondaryAction={
                    <Box sx={{ display: 'flex', opacity: 0.4, transition: 'opacity 0.2s', '&:hover': { opacity: 1 } }}>
                      <Tooltip title="Download">
                        <span>
                          <IconButton
                            edge="end"
                            aria-label="download"
                            onClick={() => onDownloadItem(item)}
                            size="small"
                            color="primary"
                            sx={{
                              mr: 0.5,
                              transition: 'all 0.2s',
                              '&:hover': {
                                transform: 'translateY(-2px)',
                                boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
                              }
                            }}
                            disabled={
                              // Disable for selected item if status is Not Available or Under Processing
                              (item.id === selectedItemId && fileDetails &&
                                (fileDetails.status === 'Not Available' || fileDetails.status === 'Under Processing')) ||
                              // Disable for any item if excelPath is missing
                              !item.excelPath
                            }
                            style={{
                              display: !item.excelPath ? 'none' : undefined,
                              color: (item.id === selectedItemId && fileDetails && fileDetails.status === 'Under Processing') ? '#1976d2' : undefined // blue for Under Processing
                            }}
                          >
                            <Download fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </Box>
                  }
                >
                  <ListItemButton
                    selected={selectedItemId === item.id}
                    onClick={() => onSelectItem(item)}
                    sx={{
                      borderLeft: selectedItemId === item.id ? 3 : 0,
                      borderColor: 'primary.main',
                      py: 1.5,
                      transition: 'all 0.2s ease',
                      position: 'relative',
                      overflow: 'hidden',
                      '&::after': selectedItemId === item.id ? {
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        width: '100%',
                        height: '2px',
                        background: (theme) =>
                          `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      } : {},
                      '&:hover': {
                        backgroundColor: (theme) =>
                          theme.palette.mode === 'light'
                            ? 'rgba(0, 0, 0, 0.05)'
                            : 'rgba(255, 255, 255, 0.08)'
                      },
                      '&.Mui-selected': {
                        backgroundColor: (theme) =>
                          theme.palette.mode === 'light'
                            ? 'rgba(0, 0, 0, 0.08)'
                            : 'rgba(255, 255, 255, 0.12)'
                      }
                    }}
                  >
                    <ListItemIcon>
                      <HistoryIcon
                        color={selectedItemId === item.id ? "primary" : "action"}
                        sx={{
                          transition: 'transform 0.2s ease',
                          transform: selectedItemId === item.id ? 'scale(1.1)' : 'scale(1)'
                        }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography
                          noWrap
                          title={item.fileName}
                          sx={{
                            fontWeight: selectedItemId === item.id ? 600 : 400,
                            color: selectedItemId === item.id ? 'primary.main' : 'text.primary'
                          }}
                        >
                          {item.fileName}
                        </Typography>
                      }
                      secondary={
                        <React.Fragment>
                          <Typography
                            variant="body2"
                            component="span"
                            display="block"
                            noWrap
                            sx={{
                              color: selectedItemId === item.id ? 'text.primary' : 'text.secondary',
                              opacity: selectedItemId === item.id ? 0.9 : 0.7
                            }}
                          >
                            {formatDate(item.uploadDate)}
                          </Typography>
                          {item.fileSize && (
                            <Box
                              component="span"
                              sx={{
                                display: 'inline-block',
                                bgcolor: (theme) =>
                                  theme.palette.mode === 'light'
                                    ? 'rgba(0, 0, 0, 0.04)'
                                    : 'rgba(255, 255, 255, 0.08)',
                                borderRadius: '4px',
                                px: 0.8,
                                py: 0.2,
                                mt: 0.5,
                                fontSize: '0.75rem',
                                color: 'text.secondary',
                                fontWeight: 500
                              }}
                            >
                              {item.fileSize}
                            </Box>
                          )}
                        </React.Fragment>
                      }
                    />
                  </ListItemButton>
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Footer Section */}
      <Box
        sx={{
          mt: 'auto',
          p: 2,
          borderTop: 1,
          borderColor: 'divider',
          background: (theme) =>
            theme.palette.mode === 'light'
              ? '#2c3e50'
              : '#1a1a1a',
          color: 'white'
        }}
      >
        {/* Version and Company Info */}
        <Box sx={{ fontSize: '0.875rem', lineHeight: 1.6 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <Typography variant="body2" sx={{ color: 'white', fontWeight: 600 }}>
              Version 1.2.0
            </Typography>
            <Button
              size="small"
              startIcon={<Assignment />}
              onClick={() => setChangelogOpen(true)}
              sx={{
                color: '#bdc3c7',
                fontSize: '0.75rem',
                minWidth: 'auto',
                px: 1,
                py: 0.25,
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }
              }}
            >
              Changelog
            </Button>
            <Button
              size="small"
              startIcon={<BugReport />}
              onClick={() => setLogsOpen(true)}
              sx={{
                color: '#bdc3c7',
                fontSize: '0.75rem',
                minWidth: 'auto',
                px: 1,
                py: 0.25,
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }
              }}
            >
              Logs
            </Button>
          </Box>
          <Typography variant="body2" sx={{ color: '#bdc3c7', mb: 0.5 }}>
            Powered by AI
          </Typography>
          <Typography variant="body2" sx={{ color: '#bdc3c7' }}>
            Developed and innovated by{' '}
            <Box
              component="span"
              sx={{
                background: (theme) =>
                  `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: 700,
                letterSpacing: '0.5px'
              }}
            >
              Accuvelocity
            </Box>
          </Typography>
        </Box>
      </Box>

      {/* Changelog Modal */}
      <ChangelogModal
        open={changelogOpen}
        onClose={() => setChangelogOpen(false)}
      />

      {/* Logs Modal */}
      <LogsModal
        open={logsOpen}
        onClose={() => setLogsOpen(false)}
      />
    </Box>
  );
};

export default HistoryPanel;
