import json
import os
import json
import os
import sys
import pandas as pd
import numpy as np
import re
import logging
import time
import traceback
from datetime import datetime
from openpyxl.styles import Pat<PERSON><PERSON>ill
from openpyxl.styles import Font, Alignment
from PyPDF2 import PdfReader
import pandas as pd
from openpyxl.styles import Font, Alignment

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()

def safe_abs(x):
    if x is None or (isinstance(x, float) and np.isnan(x)):
        return None
    return abs(x)

def creditCardExcel(timestamped_dir: str):
    
    response_dir = os.path.join(timestamped_dir, "response")
    for filename in os.listdir(response_dir):
        if filename.endswith("_grokResponse.json"):
            file_path = os.path.join(response_dir, filename)
            break
    

    with open(file_path, 'r') as file:
        data = json.load(file)
    
    os.makedirs(os.path.join(timestamped_dir, "output"), exist_ok=True)
    output_file = rf'{timestamped_dir}\output\output.xlsx'

    cat = pd.read_csv(r'config\creditcardVendor.txt', sep='|', comment='#')
    # Add a column with no-space, lowercased Description for matching
    cat['match_key'] = cat['Description'].str.replace(' ', '', regex=False).str.lower()

    previous_balance = data["Previous Balance"]
    print("Previous Balance: ", previous_balance)

    # For the DataFrame (assuming df['Description'] already exists)
    def get_vendor_coa(description, cat_df):
        desc_key = str(description).replace(' ', '').lower()
        for _, row in cat_df.iterrows():
            if row['match_key'] in desc_key:
                return row['Description'], row['Category']
        return None, None


    # Convert Transactions to DataFrame
    df = pd.DataFrame(data["Transactions"], columns=["Date", "Description", "Amount"])

    # Convert Date column to datetime (MM/DD/YYYY)
    df['Date'] = pd.to_datetime(df['Date'], format="%m/%d/%Y")

    # Add Debit and Credit columns
    df['Credit'] = df['Amount'].apply(lambda x: x if x < 0 else None)
    df['Debit'] = df['Amount'].apply(lambda x: x if x > 0 else None)

    # Optional: Make Debit and Credit positive (absolute values)
    df['Credit'] = df['Credit'].apply(safe_abs)
    df['Debit'] = df['Debit'].apply(safe_abs)

    df['Balance'] = None
    df['Amount Vendor'] = None
    df['COA'] = None

    # Reorder columns
    df = df[["Date", "Description", "Debit", "Credit", "Balance", "Amount", "Amount Vendor", "COA"]]
    df = df.rename(columns={'Amount': 'Net'})

    df[['Amount Vendor', 'COA']] = df['Description'].apply(lambda desc: pd.Series(get_vendor_coa(desc, cat)))

    # Sort by date
    df = df.sort_values(by="Date")
    df = df.reset_index(drop=True)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sheet1', startrow=4, index=False)
        workbook = writer.book
        worksheet = writer.sheets['Sheet1']
        
        bold_font = Font(bold=True)
        center_alignment = Alignment(horizontal='center', vertical='center')
        italic_font = Font(italic=True)
        
        worksheet['A1'] = "Opening Balance"
        worksheet['A1'].font = bold_font
        worksheet['A1'].alignment = center_alignment
        worksheet['A2'] = previous_balance
        worksheet['A2'].alignment = center_alignment
        
        worksheet['C1'] = "Total Debits"
        worksheet['C1'].font = bold_font
        worksheet['C1'].alignment = center_alignment
        worksheet['C2'] = "=SUBTOTAL(3,C6:C1000)"
        worksheet['C2'].alignment = center_alignment
        worksheet['C3'] = "Total Debit Amount"
        worksheet['C3'].font = bold_font
        worksheet['C3'].alignment = center_alignment
        worksheet['C4'] = "=SUBTOTAL(9,C6:C1000)"
        worksheet['C4'].alignment = center_alignment
        
        worksheet['D1'] = "Total Credits"
        worksheet['D1'].font = bold_font
        worksheet['D1'].alignment = center_alignment
        worksheet['D2'] = "=SUBTOTAL(3,D6:D1000)"
        worksheet['D2'].alignment = center_alignment
        worksheet['D3'] = "Total Credit Amount"
        worksheet['D3'].font = bold_font
        worksheet['D3'].alignment = center_alignment
        worksheet['D4'] = "=SUBTOTAL(9,D6:D1000)"
        worksheet['D4'].alignment = center_alignment
        
        last_row = len(df) + 5  # startrow=4 + header row + data rows
        
        for row in range(6, last_row + 1):  # Starting from row 6 (first data row)
                if row == 6:  # First row: Previous Balance + Credit - Debit
                    worksheet[f'E{row}'] = f'=+$A$2+D{row}-C{row}'
                else:  # Subsequent rows: Previous Balance + Credit - Debit
                    prev_row = row - 1
                    worksheet[f'E{row}'] = f'=+E{prev_row}+D{row}-C{row}'

                # Apply currency formatting to the formula cell
                worksheet[f'E{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
                
        worksheet['A2'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
        worksheet['C2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['D2'].number_format = '#,##0'  # Count format for transaction count
        worksheet['C4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts
        worksheet['D4'].number_format = '"$"#,##0.00_);("$"#,##0.00)'  # Total amounts
        
        # Format Date column (A) to show only date without time
        for row in range(6, last_row + 1):
            worksheet[f'A{row}'].number_format = 'mm/dd/yyyy'
        
        for row in range(6, last_row + 1):
                worksheet[f'C{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Format Credit column (D)
        for row in range(6, last_row + 1):
            worksheet[f'D{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
            
        # Format Credit column (E)
        for row in range(6, last_row + 1):
            worksheet[f'E{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
        
        # Format Credit column (F)
        for row in range(6, last_row + 1):
            worksheet[f'F{row}'].number_format = '"$"#,##0.00_);("$"#,##0.00)'
            
        worksheet.auto_filter.ref = "A5:H5"
        
    return output_file
        
if __name__ == "__main__":
    creditCardExcel(r"Data1\20250715_135249_453517fb")