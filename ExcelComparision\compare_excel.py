import pandas as pd
import argparse
import os
from datetime import datetime

def compare_excel_files(generated_file, original_file, output_dir=None):
    """
    Compare two Excel files with specific row skipping and column selection.

    Args:
        generated_file (str): Path to the generated Excel file
        original_file (str): Path to the original Excel file
        output_dir (str, optional): Directory to save the output file. If None, saves in the same directory as the script.

    Returns:
        str: Path to the output file containing differences
    """
    print(f"Reading generated file: {generated_file}")
    print(f"Reading original file: {original_file}")

    # Read the generated Excel file, skipping the first 4 rows
    try:
        df_generated = pd.read_excel(generated_file, skiprows=4)
        print(f"Generated file columns: {df_generated.columns.tolist()}")
    except Exception as e:
        print(f"Error reading generated file: {e}")
        return None

    # Read the original Excel file, skipping the first 2 rows
    try:
        df_original = pd.read_excel(original_file, skiprows=2)
        print(f"Original file columns: {df_original.columns.tolist()}")

        # Select only the first 5 columns from the original file
        if len(df_original.columns) >= 5:
            df_original = df_original.iloc[:, :5]
        else:
            print(f"Warning: Original file has fewer than 5 columns. Using all available columns.")
    except Exception as e:
        print(f"Error reading original file: {e}")
        return None

    # Ensure both DataFrames have the same column names for comparison
    # Assuming we need columns: Date, Description, Credit, and Debit
    expected_columns = ['Date', 'Description', 'Credit', 'Debit']

    # Handle column mapping for each DataFrame
    print("\nProcessing columns for comparison...")

    # Function to map columns based on name or position
    def map_columns(df, df_name):
        # Check if all expected columns exist
        missing_columns = [col for col in expected_columns if col not in df.columns]

        if not missing_columns:
            print(f"{df_name} file has all required columns.")
            return df[expected_columns].copy()

        print(f"{df_name} file is missing these columns: {missing_columns}")

        # If Credit and Debit are swapped in the original file, handle that case
        if df_name == "Original" and "Credit" in df.columns and "Debit" in df.columns:
            # Check if they're in reverse order
            credit_idx = df.columns.get_loc("Credit")
            debit_idx = df.columns.get_loc("Debit")

            if credit_idx > debit_idx:  # If Credit comes after Debit
                print(f"Credit and Debit columns appear to be swapped in {df_name} file. Adjusting...")
                # Create a new DataFrame with the correct column order
                new_df = pd.DataFrame()
                new_df['Date'] = df['Date']
                new_df['Description'] = df['Description']
                new_df['Credit'] = df['Credit']
                new_df['Debit'] = df['Debit']
                return new_df

        # Try to map columns by position as a fallback
        if len(df.columns) >= len(expected_columns):
            print(f"Attempting to map columns by position in {df_name} file...")
            new_df = pd.DataFrame()

            # Map Date and Description by name if they exist
            for col in ['Date', 'Description']:
                if col in df.columns:
                    new_df[col] = df[col]
                else:
                    # Use position-based mapping as fallback
                    col_idx = expected_columns.index(col)
                    if col_idx < len(df.columns):
                        new_df[col] = df.iloc[:, col_idx]
                    else:
                        print(f"Cannot map {col} column in {df_name} file.")
                        return None

            # Map Credit and Debit based on their expected positions or names
            if 'Credit' in df.columns and 'Debit' in df.columns:
                new_df['Credit'] = df['Credit']
                new_df['Debit'] = df['Debit']
            elif len(df.columns) >= 4:
                # Try to use position-based mapping
                credit_idx = expected_columns.index('Credit')
                debit_idx = expected_columns.index('Debit')

                if credit_idx < len(df.columns) and debit_idx < len(df.columns):
                    new_df['Credit'] = df.iloc[:, credit_idx]
                    new_df['Debit'] = df.iloc[:, debit_idx]
                else:
                    print(f"Cannot map Credit and Debit columns in {df_name} file.")
                    return None
            else:
                print(f"Cannot map Credit and Debit columns in {df_name} file.")
                return None

            print(f"Mapped columns in {df_name} file: {new_df.columns.tolist()}")
            return new_df

        print(f"Cannot map columns in {df_name} file.")
        return None

    # Map columns for each DataFrame
    df_generated_mapped = map_columns(df_generated, "Generated")
    df_original_mapped = map_columns(df_original, "Original")

    # Check if mapping was successful
    if df_generated_mapped is None or df_original_mapped is None:
        print("Failed to map columns for comparison.")
        return None

    # Use the mapped DataFrames for further processing
    df_generated = df_generated_mapped
    df_original = df_original_mapped

    # Convert Description columns to string to avoid type comparison issues
    print("Converting Description columns to string for consistent sorting...")
    df_generated['Description'] = df_generated['Description'].astype(str)
    df_original['Description'] = df_original['Description'].astype(str)

    # Sort both DataFrames by the Description column
    print("Sorting DataFrames by Description column...")
    df_generated.sort_values(by='Description', ascending=True, inplace=True)
    df_original.sort_values(by='Description', ascending=True, inplace=True)

    # Reset indices after sorting
    df_generated.reset_index(drop=True, inplace=True)
    df_original.reset_index(drop=True, inplace=True)

    # Find differences between the two DataFrames
    print("Finding differences between the files...")

    # Method 1: Using pandas merge with indicator
    df_diff = pd.merge(df_generated, df_original, how='outer', indicator=True)
    df_diff = df_diff[df_diff['_merge'] != 'both']

    # Method 2: Compare each column for rows that exist in both DataFrames
    # This helps identify specific cell differences
    detailed_diff = []

    # Get the common descriptions based on Description (since we sorted by it)
    common_descriptions = set(df_generated['Description']).intersection(set(df_original['Description']))
    print(f"Found {len(common_descriptions)} common descriptions between the files.")

    for desc in common_descriptions:
        # Get all rows with this description (there might be duplicates)
        gen_rows = df_generated[df_generated['Description'] == desc]
        orig_rows = df_original[df_original['Description'] == desc]

        # If there are multiple rows with the same description, we'll compare the first ones
        if not gen_rows.empty and not orig_rows.empty:
            gen_row = gen_rows.iloc[0]
            orig_row = orig_rows.iloc[0]

            # Compare each column
            for col in expected_columns:
                if col != 'Description':  # We already know Description matches
                    gen_val = gen_row[col]
                    orig_val = orig_row[col]

                    # Handle NaN values
                    if pd.isna(gen_val) and pd.isna(orig_val):
                        continue

                    # Convert values for comparison
                    try:
                        # Handle date values
                        if col == 'Date':
                            if not pd.isna(gen_val) and not pd.isna(orig_val):
                                if isinstance(gen_val, datetime) and isinstance(orig_val, datetime):
                                    gen_val = gen_val.strftime('%Y-%m-%d')
                                    orig_val = orig_val.strftime('%Y-%m-%d')
                                elif isinstance(gen_val, str) and isinstance(orig_val, str):
                                    # Try to parse strings as dates if they look like dates
                                    try:
                                        gen_date = pd.to_datetime(gen_val)
                                        orig_date = pd.to_datetime(orig_val)
                                        gen_val = gen_date.strftime('%Y-%m-%d')
                                        orig_val = orig_date.strftime('%Y-%m-%d')
                                    except:
                                        # If parsing fails, keep as strings
                                        pass

                        # Handle numeric values (Credit, Debit)
                        elif col in ['Credit', 'Debit']:
                            # Convert to float for numeric comparison if possible
                            if not pd.isna(gen_val):
                                if isinstance(gen_val, str):
                                    # Remove any currency symbols or commas
                                    gen_val = gen_val.replace('$', '').replace(',', '')
                                    gen_val = float(gen_val) if gen_val.strip() else 0.0
                                else:
                                    gen_val = float(gen_val) if not pd.isna(gen_val) else 0.0
                            else:
                                gen_val = 0.0

                            if not pd.isna(orig_val):
                                if isinstance(orig_val, str):
                                    # Remove any currency symbols or commas
                                    orig_val = orig_val.replace('$', '').replace(',', '')
                                    orig_val = float(orig_val) if orig_val.strip() else 0.0
                                else:
                                    orig_val = float(orig_val) if not pd.isna(orig_val) else 0.0
                            else:
                                orig_val = 0.0

                            # Use a small epsilon for float comparison to handle rounding errors
                            if abs(gen_val - orig_val) < 0.01:
                                continue
                    except Exception as e:
                        print(f"Error comparing values for {desc}, column {col}: {e}")
                        # If conversion fails, compare as strings
                        gen_val = str(gen_val) if not pd.isna(gen_val) else ""
                        orig_val = str(orig_val) if not pd.isna(orig_val) else ""

                    # Check if values are different
                    if gen_val != orig_val:
                        detailed_diff.append({
                            'Description': desc,
                            'Column': col,
                            'Generated Value': gen_val,
                            'Original Value': orig_val
                        })

    # Create a DataFrame from the detailed differences
    df_detailed_diff = pd.DataFrame(detailed_diff)

    # Create a timestamp for the output file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Determine the output directory
    if output_dir is None:
        output_dir = os.path.dirname(os.path.abspath(__file__))
    os.makedirs(output_dir, exist_ok=True)

    # Create output file paths
    diff_output_path = os.path.join(output_dir, f"excel_diff_{timestamp}.xlsx")

    # Create a writer to save multiple sheets in the same Excel file
    with pd.ExcelWriter(diff_output_path) as writer:
        # Save the differences found using merge
        df_diff.to_excel(writer, sheet_name='Merge Differences', index=False)

        # Save the detailed cell-by-cell differences
        if not df_detailed_diff.empty:
            df_detailed_diff.to_excel(writer, sheet_name='Cell Differences', index=False)

        # Also save the original DataFrames for reference
        df_generated.to_excel(writer, sheet_name='Generated (Processed)', index=False)
        df_original.to_excel(writer, sheet_name='Original (Processed)', index=False)

    print(f"Comparison complete. Results saved to: {diff_output_path}")
    return diff_output_path

def main():
    parser = argparse.ArgumentParser(description='Compare two Excel files with specific formatting requirements.')
    parser.add_argument('generated_file', nargs='?',
                        default=r"Data1\20250519_174723\output\transactions.xlsx",
                        help='Path to the generated Excel file')
    parser.add_argument('original_file', nargs='?',
                        default=r"test\Original\dec.xlsx",
                        help='Path to the original Excel file')
    parser.add_argument('--output-dir', help='Directory to save the output file', default=r"test")

    args = parser.parse_args()

    # Print the files being compared
    print(f"\nComparing Excel files:")
    print(f"1. Generated file: {args.generated_file}")
    print(f"2. Original file: {args.original_file}")
    print(f"Output directory: {args.output_dir}")

    # Check if files exist
    if not os.path.exists(args.generated_file):
        print(f"Error: Generated file '{args.generated_file}' does not exist.")
        return

    if not os.path.exists(args.original_file):
        print(f"Error: Original file '{args.original_file}' does not exist.")
        return

    # Create output directory if it doesn't exist
    if args.output_dir:
        os.makedirs(args.output_dir, exist_ok=True)

    # Run the comparison
    output_file = compare_excel_files(args.generated_file, args.original_file, args.output_dir)

    if output_file:
        print(f"\nComparison completed successfully!")
        print(f"Results saved to: {output_file}")
    else:
        print("\nComparison failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
