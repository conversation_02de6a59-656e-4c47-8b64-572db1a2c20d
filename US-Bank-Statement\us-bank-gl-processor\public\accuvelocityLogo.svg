<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>Accuvelocity Logo</title>
    <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1976d2;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#42a5f5;stop-opacity:1" />
        </linearGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="url(#gradient1)" x="0" y="0" width="200" height="200" rx="20"></rect>
        <g transform="translate(40.000000, 60.000000)">
            <!-- Letter A -->
            <path d="M10,80 L20,40 L30,80 M15,65 L25,65" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            <!-- Letter V -->
            <path d="M40,40 L50,80 L60,40" stroke="#FFFFFF" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            <!-- Velocity symbol (arrow) -->
            <path d="M80,50 L110,50 M105,45 L110,50 L105,55" stroke="#FFFFFF" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        </g>
    </g>
</svg>
