#!/usr/bin/env python3
"""
USB Credit Card Processor

This script combines the functionality of pdfSeparator.py and usb_bank_extractor.py.
It separates a Credit Card PDF into multiple PDFs and then processes each one
with the appropriate extraction type.

Workflow:
1. Take a PDF file as input
2. Separate it into multiple PDFs
3. Save the separated PDFs in Data1/[date_time]/PDFs/
4. Save the original PDF in Data1/[date_time]/inputPDF/
5. Process each separated PDF with the appropriate extraction type
6. Store all the extracted data in Data1/[date_time]/response/
"""

import os
import sys
import shutil
import asyncio
import json
import datetime
import boto3
import aioboto3
import aiofiles
import logging
import traceback
import uuid
from concurrent.futures import ThreadPoolExecutor
from botocore.exceptions import ClientError
from openai import OpenAI
from PyPDF2 import PdfReader, PdfWriter
from pdf2image import convert_from_path
import re
import tempfile
import time
import hashlib
import concurrent.futures
from pathlib import Path
from io import StringIO
from typing import Dict, Any, Union, List, Optional
import platform
import socket
# from .excelCreditCard import creditCardExcel

TIME_STAMP = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Import colorama for cross-platform colored terminal output
try:
    import colorama
    from colorama import Fore, Style
    colorama.init()
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter for file handler
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(file_formatter)

    # Create a custom formatter for colored console output
    if COLORAMA_AVAILABLE:
        class ColoredFormatter(logging.Formatter):
            """Custom formatter to add colors only to the log level name"""
            def __init__(self, fmt=None, datefmt=None, style='%'):
                super().__init__(fmt, datefmt, style)
                # Define colors for different log levels
                self.level_colors = {
                    'INFO': Fore.GREEN,
                    'WARNING': Fore.YELLOW,
                    'ERROR': Fore.RED
                }

            def format(self, record):
                # Save the original levelname
                original_levelname = record.levelname

                # Apply color only to the levelname if it has a defined color
                if record.levelname in self.level_colors:
                    record.levelname = f"{self.level_colors[record.levelname]}{record.levelname}{Style.RESET_ALL}"

                # Format the record with the colored levelname
                result = super().format(record)

                # Restore the original levelname for future use
                record.levelname = original_levelname

                return result

        # Create console formatter with colors
        console_formatter = ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    else:
        # Fallback to standard formatter if colorama is not available
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()
logger.info(f"Logging initialized. Log file: {os.path.join('logs', os.path.basename(logger.handlers[0].baseFilename))}")

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"

def filter_text_by_tables(ordered_text: List[Dict], tables: List[List[List[str]]]) -> List[Dict]:
    """
    Filter out text items that appear in tables to avoid duplicates.

    Args:
        ordered_text: List of text items, each with 'Text', 'Page', and position info.
        tables: List of tables, each table is a list of rows, each row is a list of cell texts.

    Returns:
        Filtered list of text items that don't appear in tables.
    """
    if not tables:
        return ordered_text

    # Create a set of normalized table cell texts
    table_text_set = set()
    for table in tables:
        for row in table:
            for cell in row:
                # Normalize the cell text (lowercase, strip whitespace)
                norm_cell = cell.strip().lower()
                if norm_cell:
                    table_text_set.add(norm_cell)

    # Filter out text items that appear in tables
    filtered_text = []
    for item in ordered_text:
        text = item['Text']
        norm_text = text.strip().lower()

        # Check if this text appears in any table cell
        if norm_text not in table_text_set:
            # Also check for partial matches (text might be part of a table cell)
            is_in_table = False
            for table_text in table_text_set:
                if norm_text in table_text or table_text in norm_text:
                    # If there's significant overlap, consider it a match
                    if len(norm_text) > 3 and (len(norm_text) >= len(table_text) * 0.7 or len(table_text) >= len(norm_text) * 0.7):
                        is_in_table = True
                        break

            if not is_in_table:
                filtered_text.append(item)

    return filtered_text

# PDF Separator Functions
def get_cache_dir():
    """Get the cache directory for storing extracted text."""
    cache_dir = os.path.join(os.path.expanduser("~"), ".pdf_separator_cache")
    try:
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        elif not os.path.isdir(cache_dir):
            # If it exists but is not a directory, append a suffix
            base_cache_dir = cache_dir
            suffix = 1
            while os.path.exists(cache_dir) and not os.path.isdir(cache_dir):
                cache_dir = f"{base_cache_dir}_{suffix}"
                suffix += 1
            os.makedirs(cache_dir)
    except Exception as e:
        print(f"Warning: Could not create cache directory: {e}")
        # Use a temporary directory as fallback
        cache_dir = os.path.join(tempfile.gettempdir(), ".pdf_separator_cache")
        os.makedirs(cache_dir, exist_ok=True)

    return cache_dir

def get_cache_key(pdf_path, page_index):
    """Generate a unique cache key for a PDF page."""
    # Get file modification time to invalidate cache when file changes
    mtime = os.path.getmtime(pdf_path)
    # Get file size as additional check
    size = os.path.getsize(pdf_path)
    # Create a unique key based on file path, modification time, size, and page index
    key = f"{pdf_path}_{mtime}_{size}_{page_index}"
    # Hash the key to create a filename-safe string
    return hashlib.md5(key.encode()).hexdigest()

def get_cached_text(pdf_path, page_index):
    """Get cached text for a PDF page if available."""
    try:
        cache_key = get_cache_key(pdf_path, page_index)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, cache_key + ".json")

        if os.path.exists(cache_file) and os.path.isfile(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    return data.get('text', '')
            except Exception as e:
                # If there's any error reading the cache, log it and ignore
                print(f"Warning: Could not read cache file {cache_file}: {e}")
                return None
    except Exception as e:
        print(f"Warning: Error accessing cache: {e}")

    return None

def save_text_to_cache(pdf_path, page_index, text):
    """Save extracted text to cache."""
    try:
        cache_key = get_cache_key(pdf_path, page_index)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, cache_key + ".json")

        # Ensure the directory exists
        os.makedirs(os.path.dirname(cache_file), exist_ok=True)

        with open(cache_file, 'w') as f:
            json.dump({'text': text}, f)
    except Exception as e:
        # If there's any error saving to cache, just log it and continue
        print(f"Warning: Could not save to cache: {e}")

def extract_text_with_textract(pdf_path, page_index, debug=False):
    """
    Extract text from a PDF page using AWS Textract with caching.
    """
    # Check cache first
    cached_text = get_cached_text(pdf_path, page_index)
    if cached_text is not None:
        if debug:
            print(f"Using cached text for page {page_index+1}")
        return cached_text

    try:
        # Convert the PDF page to an image
        if debug:
            print(f"Converting page {page_index+1} to image...")

        images = convert_from_path(pdf_path, first_page=page_index+1, last_page=page_index+1)
        if not images:
            print(f"Warning: Could not convert page {page_index+1} to image")
            return ""

        # Save the image to a temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp:
            temp_filename = temp.name
            images[0].save(temp_filename, 'PNG')

        try:
            # Call AWS Textract
            if debug:
                print(f"Calling AWS Textract for page {page_index+1}...")

            textract = boto3.client('textract')

            with open(temp_filename, 'rb') as document:
                response = textract.detect_document_text(
                    Document={'Bytes': document.read()}
                )

            # Extract text from the response
            text = ""
            for item in response['Blocks']:
                if item['BlockType'] == 'LINE':
                    text += item['Text'] + "\n"

            if debug:
                print(f"AWS Textract extracted {len(text)} characters")

            # Save to cache
            save_text_to_cache(pdf_path, page_index, text)

            return text

        finally:
            # Clean up the temporary file
            if os.path.exists(temp_filename):
                os.remove(temp_filename)

    except Exception as e:
        print(f"Error extracting text with AWS Textract: {e}")
        # Return empty string on error
        return ""

def extract_text_from_page(page_obj, pdf_path, page_index, debug=False):
    """
    Extract text from a PDF page using AWS Textract.
    Falls back to PyPDF2 if AWS Textract fails.
    """
    # Try AWS Textract first
    text = extract_text_with_textract(pdf_path, page_index, debug)

    # If AWS Textract failed or returned empty text, fall back to PyPDF2
    if not text:
        if debug:
            print("AWS Textract failed or returned empty text, falling back to PyPDF2...")
        text = page_obj.extract_text() or ""

        # Clean up the text - remove extra whitespace and normalize
        if text:
            text = re.sub(r'\s+', ' ', text).strip()

    return text

# USB Bank Extractor Classes
class Config:
    """Configuration manager for the application."""

    _section_name: str = None
    _entity_name: str = None
    _full_config: Dict[str, Any] = {}
    
    # @staticmethod
    # def load_config(config_path: str = 'config/config.json') -> Dict[str, Any]:
    #     """Load configuration settings from a JSON file."""
    #     try:
    #         with open(config_path, 'r') as file:
    #             return json.load(file)
    #     except Exception as e:
    #         logging.error(f"Failed to load config file {config_path}: {e}")
    #         return {}
    
    @classmethod
    def initialize(cls, config_path: str, section_name: str, entity_name: str):
        """Set the section and entity to be used globally."""
        cls._section_name = section_name
        cls._entity_name = entity_name
        try:
            with open(config_path, 'r') as file:
                cls._full_config = json.load(file)
        except Exception as e:
            logging.error(f"Failed to load config file {config_path}: {e}")
            cls._full_config = {}

    @classmethod
    def load_config(cls) -> Dict[str, Any]:
        """Return config for the selected section and entity."""
        try:
            return cls._full_config.get(cls._section_name, {}).get(cls._entity_name, {})
        except Exception as e:
            logging.error(f"Failed to retrieve config: {e}")
            return {}

    @staticmethod
    def load_system_prompt(folder_path: str, file_name: str) -> str:
        """Load the system prompt from a text file."""
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return file.read()
        except Exception as e:
            logging.error(f"Failed to load system prompt file {file_path}: {e}")
            return ""

    @staticmethod
    def load_response_format(folder_path: str, file_name: str) -> Dict[str, Any]:
        """Load the response format from a JSON file."""
        file_path = os.path.join(folder_path, file_name)
        try:
            with open(file_path, 'r') as file:
                return json.load(file)
        except Exception as e:
            logging.error(f"Failed to load response format file {file_path}: {e}")
            return {}


class AWSTextract:
    """Handles AWS Textract operations for PDF text extraction."""

    @staticmethod
    async def upload_to_s3(s3_client, local_file_path: str, bucket_name: str, object_name: Optional[str] = None) -> str:
        """Upload a file to an S3 bucket."""
        if object_name is None:
            object_name = f"{uuid.uuid4().hex}_{os.path.basename(local_file_path)}"

        try:
            await s3_client.upload_file(local_file_path, bucket_name, object_name)
            logging.info(f"Uploaded {local_file_path} to {bucket_name}/{object_name}")
            return object_name
        except ClientError as e:
            logging.error(f"Error uploading to S3: {e}")
            sys.exit(1)

    @staticmethod
    async def start_document_text_detection(textract_client, bucket_name: str, object_name: str,
                                           job_tag: str, notification_channel: Optional[Dict] = None) -> str:
        """Start an asynchronous text detection job with AWS Textract."""
        try:
            kwargs = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'JobTag': job_tag
            }

            if notification_channel:
                kwargs['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_text_detection(**kwargs)
            return response['JobId']
        except ClientError as e:
            logging.error(f"Error starting text detection job: {e}")
            sys.exit(1)

    @staticmethod
    async def start_document_analysis(textract_client, bucket_name: str, object_name: str,
                                     job_tag: str, notification_channel: Optional[Dict] = None) -> str:
        """Start an asynchronous document analysis job with Textract for tables."""
        try:
            kwargs = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'FeatureTypes': ['TABLES'],
                'JobTag': job_tag
            }

            if notification_channel:
                kwargs['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_analysis(**kwargs)
            return response['JobId']
        except ClientError as e:
            logging.error(f"Error starting document analysis: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_text_detection(textract_client, job_id: str) -> List[Dict]:
        """Get results from a text detection job."""
        blocks = []
        pagination_token = None

        try:
            while True:
                kwargs = {'JobId': job_id}
                if pagination_token:
                    kwargs['NextToken'] = pagination_token

                response = await textract_client.get_document_text_detection(**kwargs)

                if response['JobStatus'] == 'SUCCEEDED':
                    blocks.extend(response['Blocks'])

                    if 'NextToken' in response:
                        pagination_token = response['NextToken']
                    else:
                        break
                elif response['JobStatus'] == 'FAILED':
                    logging.error("Text detection job failed")
                    sys.exit(1)
                else:
                    await asyncio.sleep(5)  # Wait before checking again

            return blocks
        except ClientError as e:
            logging.error(f"Error getting text detection results: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_analysis(textract_client, job_id: str) -> List[Dict]:
        """Get results from a document analysis job."""
        blocks = []
        pagination_token = None

        try:
            while True:
                kwargs = {'JobId': job_id}
                if pagination_token:
                    kwargs['NextToken'] = pagination_token

                response = await textract_client.get_document_analysis(**kwargs)

                if response['JobStatus'] == 'SUCCEEDED':
                    blocks.extend(response['Blocks'])

                    if 'NextToken' in response:
                        pagination_token = response['NextToken']
                    else:
                        break
                elif response['JobStatus'] == 'FAILED':
                    logging.error("Document analysis job failed")
                    sys.exit(1)
                else:
                    await asyncio.sleep(5)  # Wait before checking again

            return blocks
        except ClientError as e:
            logging.error(f"Error getting document analysis results: {e}")
            sys.exit(1)

    @staticmethod
    def get_text_in_order(blocks: List[Dict]) -> List[Dict]:
        """Process Textract blocks to extract text in reading order."""
        text_items = []

        for block in blocks:
            if block['BlockType'] == 'LINE':
                text_items.append({
                    'Page': block.get('Page', 1),
                    'Text': block.get('Text', ''),
                    'X1': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0),
                    'Y1': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0),
                    'X2': block.get('Geometry', {}).get('BoundingBox', {}).get('Left', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Width', 0),
                    'Y2': block.get('Geometry', {}).get('BoundingBox', {}).get('Top', 0) +
                          block.get('Geometry', {}).get('BoundingBox', {}).get('Height', 0)
                })

        # Sort by page, then by Y coordinate (top to bottom), then by X coordinate (left to right)
        return sorted(text_items, key=lambda x: (x['Page'], x['Y1'], x['X1']))

    @staticmethod
    def extract_tables_from_blocks(blocks: List[Dict]) -> List[List[List[str]]]:
        """
        Extracts tables from Textract response blocks.

        Args:
            blocks: List of blocks from Textract response.

        Returns:
            List of tables, each table is a list of rows, each row is a list of cell texts.
        """
        tables = []
        table_blocks = [block for block in blocks if block['BlockType'] == 'TABLE']

        for table in table_blocks:
            cell_blocks = []

            # Extract CELL blocks related to the current table
            relationships = table.get('Relationships', [])
            for rel in relationships:
                if rel['Type'] == 'CHILD':
                    for child_id in rel['Ids']:
                        cell = next((b for b in blocks if b['Id'] == child_id and b['BlockType'] == 'CELL'), None)
                        if cell:
                            cell_blocks.append(cell)

            if not cell_blocks:
                continue

            # Sort cells by row and column
            sorted_cells = sorted(cell_blocks, key=lambda x: (x.get('RowIndex', 0), x.get('ColumnIndex', 0)))

            # Determine the number of rows and columns
            max_row = max(cell.get('RowIndex', 0) for cell in sorted_cells)
            max_col = max(cell.get('ColumnIndex', 0) for cell in sorted_cells)

            # Initialize table structure
            table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

            for cell in sorted_cells:
                row = cell.get('RowIndex', 0) - 1  # Zero-based index
                col = cell.get('ColumnIndex', 0) - 1  # Zero-based index
                text = AWSTextract.get_text_for_block(blocks, cell)
                if 0 <= row < max_row and 0 <= col < max_col:
                    table_data[row][col] = text
                else:
                    logging.warning(f"Cell position out of bounds. Row: {row+1}, Column: {col+1}")

            tables.append(table_data)

        return tables

    @staticmethod
    def get_text_for_block(blocks: List[Dict], cell_block: Dict) -> str:
        """
        Retrieves the text associated with a cell block.

        Args:
            blocks: List of all blocks.
            cell_block: The cell block for which text is to be retrieved.

        Returns:
            Concatenated text within the cell.
        """
        text = []
        relationships = cell_block.get('Relationships', [])
        for rel in relationships:
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    child = next((b for b in blocks if b['Id'] == child_id), None)
                    if child:
                        if child['BlockType'] == 'WORD':
                            text.append(child.get('Text', ''))
                        elif child['BlockType'] == 'SELECTION_ELEMENT' and child.get('SelectionStatus') == 'SELECTED':
                            text.append("X")
        return ' '.join(text)


class OpenAIExtractor:
    """Handles data extraction using OpenAI API."""

    def __init__(self, file_path: str, extraction_type: str, clientName="XAI") -> None:
        """Initialize the OpenAI extractor."""
        self.config = Config.load_config()
        self.clientName = clientName
        if self.clientName=="OpenAI":
            self.client = OpenAI()
        elif self.clientName=="XAI":
            self.client = OpenAI(
                api_key="************************************************************************************",
                base_url="https://api.x.ai/v1",
                )
            

        self.file_path = file_path
        self.folder_path = os.path.dirname(file_path)
        self.file_name = os.path.basename(file_path)
        self.file_name_without_ext = os.path.splitext(self.file_name)[0]
        self.extraction_type = extraction_type
        
        self.system_prompt_path = self.config.get(extraction_type, {}).get("systemPromptFilePath", "")
        if not self.system_prompt_path:
            raise ValueError(f"❌ System prompt not found for extraction type: {extraction_type}")

        # Set the response format path
        self.response_format_path = (self.config.get("responseFormatFilePath", "resource/ResponseFormat_grok_V2.json"))
        self.api_response_path = f"Data1/{TIME_STAMP}/apiResponses"

    async def load_configurations(self) -> None:
        """Load necessary configurations and prepare data for API call."""
        text_file_path = os.path.join(
            self.folder_path,
            self.file_name_without_ext,
            f"{self.file_name_without_ext}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        # Extract text with AWS Textract if not present
        if not os.path.exists(text_file_path):
            await self.extract_text_from_pdf()

        # Load the user prompt
        with open(text_file_path, 'r') as file:
            self.user_content = file.read()

        # Load the system prompt
        try:
            try:
                with open(self.system_prompt_path, 'r') as file:
                    self.system_prompt = file.read()
            except:
                with open(self.system_prompt_path, 'r', encoding='utf-8') as file:
                    self.system_prompt = file.read()
        except Exception as e:
            logging.error(f"Error loading system prompt: {e}")
            self.system_prompt = f"Extract {self.extraction_type} information from the Credit Card."

        # Load the response format
        try:
            with open(self.response_format_path, 'r') as file:
                response_formats = json.load(file)

            # Get the response format for the specific extraction type
            if self.extraction_type in response_formats:
                self.response_format = response_formats[self.extraction_type]
            else:
                logging.error(f"No response format found for extraction type: {self.extraction_type}")
                self.response_format = {"format": {"type": "json_object"}}
        except Exception as e:
            logging.error(f"Error loading response format: {e}")
            self.response_format = {"format": {"type": "json_object"}}

    async def extract_text_from_pdf(self) -> None:
        """Extract text from PDF using AWS Textract."""
        await extract_by_aws_textract(self.file_path)

    async def call_openai_api(self) -> Any:
        """Call OpenAI API to extract structured data asynchronously."""
        try:
            logging.info("------------- Started extraction by OpenAI ------------------")

            # Add JSON instruction to the system prompt
            system_prompt_with_json = self.system_prompt + "\nPlease provide your response in JSON format."

            # Define the synchronous function to run in a separate thread
            def make_api_call():
                # Use only the o4-mini model API format
                if self.clientName == "OpenAI": 
                    object = self.client.responses.create(
                        model="o4-mini",
                        input=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        text = self.response_format,
                        reasoning={"effort": "high"},
                        metadata = {
                            "Project": "US Credit Card Processor",
                            "ResponsibleDeveloper": "Meetul Agrawal",
                            "Hostname": socket.gethostname(),
                            "ipAddress": socket.gethostbyname(socket.gethostname()),
                            "Platform": platform.system(),
                            "User": os.getlogin() if hasattr(os, 'getlogin') else "unknown",
                            "FunctionKey": self.extraction_type,
                            "ClientName": "Patosi"
                        }
                    )
                elif self.clientName == "XAI":
                    object = self.client.chat.completions.create(
                        model="grok-3-mini",
                        messages=[
                            {"role": "system", "content": system_prompt_with_json},
                            {"role": "user", "content": str(self.user_content)}
                        ],
                        response_format=self.response_format.get("format"),
                        seed=33,
                        temperature=0,
                        reasoning_effort="high"
                    )
                    # Store the object in apiResponses directory as well
                    try:
                        import os, json
                        api_responses_dir = os.path.join("Data1", TIME_STAMP, "apiResponses")
                        os.makedirs(api_responses_dir, exist_ok=True)
                        unique_id = uuid.uuid4().hex
                        response_file_path = os.path.join(
                            api_responses_dir,
                            f"{self.file_name_without_ext}_{self.extraction_type}_{unique_id}_rawXAIResponse.json"
                        )
                        # Try to convert the object to dict if possible
                        if hasattr(object, 'model_dump_json'):
                            with open(response_file_path, 'w', encoding='utf-8') as f:
                                f.write(object.model_dump_json(indent=2))
                        elif hasattr(object, 'to_dict'):
                            with open(response_file_path, 'w', encoding='utf-8') as f:
                                json.dump(object.to_dict(), f, ensure_ascii=False, indent=2)
                        else:
                            # Fallback: try to serialize as dict
                            with open(response_file_path, 'w', encoding='utf-8') as f:
                                json.dump(object.__dict__ if hasattr(object, '__dict__') else str(object), f, ensure_ascii=False, indent=2)
                    except Exception as e:
                        import logging
                        logging.error(f"Failed to save XAI object to apiResponses: {e}")
                return object

            # Run the API call in a separate thread to avoid blocking the event loop
            try:
                # Try using asyncio.to_thread (Python 3.9+)
                import asyncio
                response = await asyncio.to_thread(make_api_call)
            except (ImportError, AttributeError):
                # Fallback for Python < 3.9
                from concurrent.futures import ThreadPoolExecutor
                with ThreadPoolExecutor() as executor:
                    response = await asyncio.get_event_loop().run_in_executor(executor, make_api_call)

            logging.info("------------- OpenAI Extraction completed ------------------")
            self.response = response
            return response
        except Exception as e:
            logging.error(f"Exception occurred during OpenAI API call: {e}")
            return None

    def save_response(self, response_dir: str = None) -> None:
        """
        Save the OpenAI API response to a JSON file and the parsed content to TrueData folder.

        Args:
            response_dir: Optional custom directory to save the parsed content
        """
        if not hasattr(self, 'response') or self.response is None:
            logging.error("No response to save")
            return

        # Create directory if it doesn't exist
        vendor_response_path = os.path.join(self.api_response_path, self.extraction_type)
        os.makedirs(vendor_response_path, exist_ok=True)

        # Save response to JSON file
        response_file_path = os.path.join(
            vendor_response_path,
            f"{self.file_name_without_ext}{GPT_RESPONSE_SUFFIX}.json"
        )

        try:
            # Extract the content from the response object
            response_dict = {}
            parsed_content = None

            # Handle o4-mini model response structure
            if hasattr(self.response, 'model'):
                response_dict['model'] = self.response.model

            # Extract content from the response based on its structure
            content_text = None

            # Try to extract from o4-mini response structure
            if hasattr(self.response, 'output') and len(self.response.output) > 0:
                for output_item in self.response.output:
                    if hasattr(output_item, 'content') and len(output_item.content) > 0:
                        for content_item in output_item.content:
                            if hasattr(content_item, 'text'):
                                content_text = content_item.text
                                break
                        if content_text:
                            break

            # Fallback to traditional structure if o4-mini structure not found
            if not content_text and hasattr(self.response, 'choices') and len(self.response.choices) > 0:
                if hasattr(self.response.choices[0], 'message') and hasattr(self.response.choices[0].message, 'content'):
                    content_text = self.response.choices[0].message.content

            # Store the content in the response dictionary
            if content_text:
                response_dict['content'] = content_text

                # Try to parse the content as JSON if it's a string
                try:
                    try:
                        if hasattr(self.response, 'usage'):
                            usage_data = self.response.usage.__dict__
                            response_dict['token_usage'] = {
                                'input_tokens': usage_data.get('input_tokens'),
                                'output_tokens': usage_data.get('output_tokens'),
                                'total_tokens': usage_data.get('total_tokens')
                            }
                        elif hasattr(self.response, 'usage_metadata'):
                            metadata_data = self.response.usage_metadata.__dict__
                            response_dict['token_usage'] = {
                                'prompt_tokens': metadata_data.get('prompt_tokens'),
                                'completion_tokens': metadata_data.get('completion_tokens'),
                                'total_tokens': metadata_data.get('total_tokens')
                            }
                    except Exception as ex:
                        logging.warning(f"Failed to extract token usage info: {ex}")
                    if isinstance(content_text, str):
                        # Clean up the content text - remove markdown code block markers if present
                        cleaned_text = content_text

                        # Remove ```json at the beginning if present
                        if cleaned_text.strip().startswith("```json"):
                            cleaned_text = cleaned_text.strip()[7:].strip()

                        # Remove ``` at the end if present
                        if cleaned_text.strip().endswith("```"):
                            cleaned_text = cleaned_text.strip()[:-3].strip()

                        # Try to parse the cleaned text
                        try:
                            parsed_content = json.loads(cleaned_text)
                            response_dict['parsed_content'] = parsed_content
                        except json.JSONDecodeError as e:
                            # If parsing fails, try to fix common issues
                            logging.warning(f"Initial JSON parsing failed: {str(e)}. Attempting to fix JSON...")

                            # Try to fix truncated JSON by adding missing closing brackets
                            fixed_text = cleaned_text

                            # Count opening and closing braces/brackets
                            open_braces = fixed_text.count('{')
                            close_braces = fixed_text.count('}')
                            open_brackets = fixed_text.count('[')
                            close_brackets = fixed_text.count(']')

                            # Add missing closing braces/brackets
                            if open_braces > close_braces:
                                fixed_text += '}' * (open_braces - close_braces)
                            if open_brackets > close_brackets:
                                fixed_text += ']' * (open_brackets - close_brackets)

                            # Try parsing again with the fixed text
                            try:
                                parsed_content = json.loads(fixed_text)
                                response_dict['parsed_content'] = parsed_content
                                logging.info("Successfully fixed and parsed JSON")
                            except json.JSONDecodeError as e2:
                                # If it still fails, try a more aggressive approach for specific extraction types
                                if self.extraction_type == "creditInfo":
                                    # For credit info, try to extract the deposits array
                                    try:
                                        # Create a simple JSON structure with just the deposits array
                                        deposits_match = re.search(r'"deposits"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if deposits_match:
                                            deposits_text = deposits_match.group(1).strip()
                                            # Fix any truncated JSON objects in the array
                                            deposits_text = re.sub(r',\s*$', '', deposits_text)  # Remove trailing commas
                                            deposits_json = f'{{"deposits":[{deposits_text}]}}'
                                            parsed_content = json.loads(deposits_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted deposits array from truncated JSON")
                                        else:
                                            # Try with CreditsInfo instead
                                            credits_match = re.search(r'"CreditsInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                            if credits_match:
                                                credits_text = credits_match.group(1).strip()
                                                credits_text = re.sub(r',\s*$', '', credits_text)
                                                credits_json = f'{{"CreditsInfo":[{credits_text}]}}'
                                                parsed_content = json.loads(credits_json)
                                                response_dict['parsed_content'] = parsed_content
                                                logging.info("Successfully extracted CreditsInfo array from truncated JSON")
                                            else:
                                                raise Exception("Could not find deposits or CreditsInfo array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract credits array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "checkNumberInorder":
                                    # For check number info, try to extract the CheckNumberinOrder array
                                    try:
                                        # Look for the CheckNumberinOrder array
                                        match = re.search(r'"CheckNumberinOrder"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            # Fix any truncated JSON objects in the array
                                            array_content = re.sub(r',\s*$', '', array_content)  # Remove trailing commas

                                            # Try to fix the array content by completing any incomplete objects
                                            array_items = []
                                            current_item = ""
                                            brace_count = 0

                                            # First try to parse the whole array
                                            try:
                                                check_json = f'{{"CheckNumberinOrder":[{array_content}]}}'
                                                parsed_content = json.loads(check_json)
                                                response_dict['parsed_content'] = parsed_content
                                                logging.info("Successfully extracted CheckNumberinOrder array from truncated JSON")
                                                return
                                            except json.JSONDecodeError:
                                                # If that fails, try to parse each object individually
                                                logging.info("Trying to parse individual check objects from array")

                                                # Split the array content by commas outside of objects
                                                items = []
                                                current = ""
                                                depth = 0

                                                for char in array_content:
                                                    if char == '{':
                                                        depth += 1
                                                    elif char == '}':
                                                        depth -= 1

                                                    current += char

                                                    if depth == 0 and char == '}':
                                                        items.append(current)
                                                        current = ""
                                                    elif depth == 0 and char == ',':
                                                        current = ""

                                                # Try to parse each item
                                                for item in items:
                                                    try:
                                                        obj = json.loads(item)
                                                        array_items.append(obj)
                                                    except json.JSONDecodeError:
                                                        logging.warning(f"Skipping invalid check object: {item}")

                                                if array_items:
                                                    parsed_content = {"CheckNumberinOrder": array_items}
                                                    response_dict['parsed_content'] = parsed_content
                                                    logging.info(f"Successfully extracted {len(array_items)} valid items from CheckNumberinOrder array")
                                                else:
                                                    raise Exception("Could not extract any valid check objects from array")
                                        else:
                                            raise Exception("Could not find CheckNumberinOrder array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract CheckNumberinOrder array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "debitInfo":
                                    # For debit info, try to extract the DebitInfo array
                                    try:
                                        match = re.search(r'"DebitInfo"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            array_content = re.sub(r',\s*$', '', array_content)
                                            debit_json = f'{{"DebitInfo":[{array_content}]}}'
                                            parsed_content = json.loads(debit_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted DebitInfo array from truncated JSON")
                                        else:
                                            raise Exception("Could not find DebitInfo array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract DebitInfo array: {str(e3)}")
                                        raise e2
                                elif self.extraction_type == "dailyEndingBalance":
                                    # For daily ending balance, try to extract the dailyEndingBalance array
                                    try:
                                        match = re.search(r'"dailyEndingBalance"\s*:\s*\[(.*?)(?:\]\s*}|\Z)', fixed_text, re.DOTALL)
                                        if match:
                                            array_content = match.group(1).strip()
                                            array_content = re.sub(r',\s*$', '', array_content)
                                            balance_json = f'{{"dailyEndingBalance":[{array_content}]}}'
                                            parsed_content = json.loads(balance_json)
                                            response_dict['parsed_content'] = parsed_content
                                            logging.info("Successfully extracted dailyEndingBalance array from truncated JSON")
                                        else:
                                            raise Exception("Could not find dailyEndingBalance array in JSON")
                                    except Exception as e3:
                                        logging.warning(f"Could not extract dailyEndingBalance array: {str(e3)}")
                                        raise e2
                                else:
                                    # For other types, just raise the original error
                                    raise e2
                except json.JSONDecodeError as e:
                    # If it's not valid JSON, keep it as a string
                    logging.warning(f"Could not parse response content as JSON: {content_text[:100]}... Error: {str(e)}")
            response_dict['system_info'] = {
                    "hostname": socket.gethostname(),
                    "ip_address": socket.gethostbyname(socket.gethostname()),
                    "platform": platform.system(),
                    "platform_version": platform.version(),
                    "processor": platform.processor(),
                    "python_version": platform.python_version(),
                    "timestamp": datetime.datetime.now().isoformat(),
                    "user": os.getlogin() if hasattr(os, 'getlogin') else "unknown"
                }
            # Save the extracted data
            with open(response_file_path, 'w', encoding='utf-8') as file:
                json.dump(response_dict, file, ensure_ascii=False, indent=4)

            logging.info(f"Response saved to {response_file_path}")

            # Save parsed content to TrueData folder or custom response directory
            if parsed_content:
                if response_dir:
                    # Use the custom response directory
                    true_data_path = response_dir
                else:
                    # Create TrueData folder structure if it doesn't exist
                    true_data_base_path = "data/TrueData"
                    os.makedirs(true_data_base_path, exist_ok=True)

                    # Create subfolder for extraction type if it doesn't exist
                    for folder in ["creditInfo", "debitInfo", "checkNumberInorder", "dailyEndingBalance", "OpeningPageInfo"]:
                        os.makedirs(os.path.join(true_data_base_path, folder), exist_ok=True)

                    # Use the default TrueData path
                    true_data_path = os.path.join(true_data_base_path, self.extraction_type)

                # Ensure the directory exists
                os.makedirs(true_data_path, exist_ok=True)

                # Save parsed content to the corresponding subfolder
                true_data_file_path = os.path.join(
                    true_data_path,
                    f"{self.file_name_without_ext}_trueData.json"
                )

                with open(true_data_file_path, 'w', encoding='utf-8') as file:
                    json.dump(parsed_content, file, ensure_ascii=False, indent=4)

                logging.info(f"Parsed content saved to {true_data_file_path}")

                # Also save to the response directory if provided
                if response_dir:
                    response_file_path = os.path.join(
                        response_dir,
                        f"{self.file_name_without_ext}_trueData.json"
                    )

                    with open(response_file_path, 'w', encoding='utf-8') as file:
                        json.dump(parsed_content, file, ensure_ascii=False, indent=4)

                    logging.info(f"Parsed content also saved to {response_file_path}")

        except Exception as e:
            logging.error(f"Error saving response: {e}")

    def move_file(self) -> None:
        """Move processed files to the processed folder."""
        # Create processed folder if it doesn't exist
        processed_path = os.path.join(self.folder_path, PROCESSED_FOLDER_NAME)
        os.makedirs(processed_path, exist_ok=True)

        try:
            # Move PDF file
            pdf_dest = os.path.join(processed_path, self.file_name)
            if os.path.exists(pdf_dest):
                os.remove(pdf_dest)  # Remove existing file if it exists
            shutil.move(self.file_path, processed_path)

            # Move folder containing extracted text
            source_folder = os.path.join(self.folder_path, self.file_name_without_ext)
            dest_folder = os.path.join(processed_path, self.file_name_without_ext)

            if os.path.exists(source_folder):
                if os.path.exists(dest_folder):
                    # If destination exists, remove it first
                    shutil.rmtree(dest_folder)
                shutil.move(source_folder, processed_path)

            logging.info(f"Moved files to {processed_path}")
        except Exception as e:
            logging.error(f"Error moving files: {e}")

async def extract_by_aws_textract(local_pdf_path: str, api_responses_dir: str = None) -> Dict:
    """Extract text and tables from a PDF file using AWS Textract."""
    # Configuration
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name
    job_tag = "ExtractTextJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    # Derive base filename from the input PDF file name
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    # Create output folder
    output_folder = api_responses_dir
    os.makedirs(output_folder, exist_ok=True)

    # Check if this is a page from a multi-page PDF
    is_multi_page = False
    multi_page_dir = None
    if "MultiPageResponses" in local_pdf_path:
        is_multi_page = True
        # Get the parent directory (which should be the PDF name folder)
        multi_page_dir = os.path.dirname(local_pdf_path)

    # Initialize AWS clients
    session = aioboto3.Session()
    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Upload PDF to S3
        logging.info(f"Uploading {local_pdf_path} to S3...")
        s3_key = await AWSTextract.upload_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Step 1: Start text detection for regular text
        logging.info("Starting text detection with Textract...")
        text_job_id = await AWSTextract.start_document_text_detection(
            textract_client, bucket_name, s3_key, job_tag, notification_channel
        )

        # Step 2: Start document analysis for tables
        logging.info("Starting document analysis for tables with Textract...")
        table_job_id = await AWSTextract.start_document_analysis(
            textract_client, bucket_name, s3_key, f"{job_tag}_Tables", notification_channel
        )

        # Get text detection results
        logging.info("Polling for text detection results...")
        text_blocks = await AWSTextract.get_document_text_detection(textract_client, text_job_id)

        # Get document analysis results (for tables)
        logging.info("Polling for document analysis results (tables)...")
        table_blocks = await AWSTextract.get_document_analysis(textract_client, table_job_id)

        # Extract text in reading order
        logging.info("Extracting text in reading order...")
        ordered_text = AWSTextract.get_text_in_order(text_blocks)
        
        aws_ordered_text_path = os.path.join(api_responses_dir, f"{base_filename}_awsResponse.json")
        async with aiofiles.open(aws_ordered_text_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(ordered_text, ensure_ascii=False, indent=4))
        logging.info(f"Ordered text saved to {aws_ordered_text_path}")

        # Extract tables
        logging.info("Extracting tables from results...")
        tables = AWSTextract.extract_tables_from_blocks(table_blocks)
        logging.info(f"Found {len(tables)} table(s).")

        # Filter out text that appears in tables to avoid duplicates
        logging.info("Filtering out text that appears in tables...")
        filtered_ordered_text = filter_text_by_tables(ordered_text, tables)
        logging.info(f"Filtered out {len(ordered_text) - len(filtered_ordered_text)} text items that appear in tables.")

        # Save extracted text and tables to file
        text_json_filename = os.path.join(
            output_folder, f"{base_filename}{TEXT_CONVERSION_SUFFIX}.txt"
        )

        try:
            # Create combined text file with both text and tables
            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                # Write Text section
                await f.write("Text\n")

                # Format the text in CSV-like format
                csv_content = "Page No., Text, X1, Y1, X2, Y2\n"
                for item in filtered_ordered_text:  # Use filtered text here
                    csv_content += f"{item['Page']}, {item['Text']}, {item['X1']}, {item['Y1']}, {item['X2']}, {item['Y2']}\n"

                await f.write(csv_content)
                await f.write("\n\n")

                # Write Tables section
                for idx, table in enumerate(tables, start=1):
                    await f.write(f"Table-{idx}\n")

                    # Use StringIO to accumulate the CSV data in memory
                    output = StringIO()
                    import csv
                    writer = csv.writer(output)

                    # Write each row of the table
                    for row in table:
                        # Replace empty cells with '0'
                        new_row = [cell if cell.strip() != '' else '0' for cell in row]
                        writer.writerow(new_row)

                    # Get the CSV content as a string
                    table_data = output.getvalue()

                    # Write the table data to the file
                    await f.write(table_data)
                    await f.write("\n\n")

            # If this is a page from a multi-page PDF, also save the AWS Textract responses to the multi-page directory
            if is_multi_page and multi_page_dir:
                # Save the AWS Textract text response
                aws_text_response_filename = os.path.join(
                    multi_page_dir, f"{base_filename}_aws_text_response.json"
                )

                # Convert blocks to a serializable format
                serializable_text_blocks = []
                for block in text_blocks:
                    # Convert any non-serializable objects to strings or simple types
                    serializable_block = {}
                    for key, value in block.items():
                        if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                            serializable_block[key] = value
                        else:
                            serializable_block[key] = str(value)
                    serializable_text_blocks.append(serializable_block)

                # Save the AWS text response
                async with aiofiles.open(aws_text_response_filename, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(serializable_text_blocks, ensure_ascii=False, indent=4))

                logging.info(f"AWS Textract text response saved to {aws_text_response_filename}")

                # Save the AWS Textract table response
                aws_table_response_filename = os.path.join(
                    multi_page_dir, f"{base_filename}_aws_table_response.json"
                )

                # Convert blocks to a serializable format
                serializable_table_blocks = []
                for block in table_blocks:
                    # Convert any non-serializable objects to strings or simple types
                    serializable_block = {}
                    for key, value in block.items():
                        if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                            serializable_block[key] = value
                        else:
                            serializable_block[key] = str(value)
                    serializable_table_blocks.append(serializable_block)

                # Save the AWS table response
                async with aiofiles.open(aws_table_response_filename, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(serializable_table_blocks, ensure_ascii=False, indent=4))
                    
        

                logging.info(f"AWS Textract table response saved to {api_responses_dir}")

            logging.info(f"Extracted text and tables saved to {text_json_filename}.")
            return {"status": "success", "file_path": text_json_filename}
        except Exception as e:
            logging.error(f"Error writing to file: {e}")
            logging.error(traceback.format_exc())
            return {"status": "error", "error": str(e)}

        if api_responses_dir:
            os.makedirs(api_responses_dir, exist_ok=True)
            # 1. Copy or write the extracted prompt TXT
            target_txt = os.path.join(api_responses_dir, os.path.basename(text_json_filename))
            shutil.copyfile(text_json_filename, target_txt)

            # 2. Write AWS Textract text response
            aws_text_json_path = os.path.join(api_responses_dir, f"{base_filename}_aws_text_response.json")
            async with aiofiles.open(aws_text_json_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(serializable_text_blocks, ensure_ascii=False, indent=4))

            # 3. Write AWS Textract table response
            aws_table_json_path = os.path.join(api_responses_dir, f"{base_filename}_aws_table_response.json")
            async with aiofiles.open(aws_table_json_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(serializable_table_blocks, ensure_ascii=False, indent=4))

def openAICall(timestamped_dir):
    
    try:
        # Load the user prompt
        api_responses_dir = os.path.join(timestamped_dir, "apiResponses")
        response_dir = os.path.join(timestamped_dir, "response")
        
        for filename in os.listdir(api_responses_dir):
            if filename.endswith("_strUserPrompt.txt"):
                file_path = os.path.join(api_responses_dir, filename)
                with open(file_path, "r", encoding="utf-8") as file:
                    userPrompt = file.read()
                break
        
        # Get the input PDF name
        input_pdf_dir = os.path.join(timestamped_dir, "inputPDF")
        for filename in os.listdir(input_pdf_dir):
            if filename.lower().endswith(".pdf"):
                filename_without_ext = os.path.splitext(filename)[0]
                break

        # Load Bank Config
        bankConfig = Config.load_config()

        # Load the system prompt
        systemPrompt = bankConfig["systemPromptFilePath"]
        with open(systemPrompt, "r", encoding="utf-8") as f:
            systemPromptText = f.read()
        systemPromptWithJson = systemPromptText + "\nPlease provide your response in JSON format."
        
        # Load the response format
        responseFormatPath = bankConfig["responseFormatFilePath"]
        with open(responseFormatPath, "r", encoding="utf-8") as f:
            responseFormat = json.load(f)  # Now it's a dictionary
        
        # Initialize the OpenAI client
        client = OpenAI(
            api_key="************************************************************************************",
            base_url="https://api.x.ai/v1"
        )
        
        # Call the OpenAI API
        object = client.chat.completions.create(
                                model="grok-3-mini",
                                messages=[
                                    {"role": "system", "content": systemPromptWithJson},
                                    {"role": "user", "content": str(userPrompt)}
                                ],
                                response_format=responseFormat.get("CreditCardSummary").get("format"),
                                seed=33,
                                temperature=0,
                                reasoning_effort="high"
                            )
        
        strResponseContent = object.choices[0].message.content
        # Extract JSON portion
        jsonMatch = re.search(r'\{[\s\S]+\}', strResponseContent)
        if jsonMatch:
            jsonData = json.loads(jsonMatch.group())
            outputPath = os.path.join(response_dir, f"{filename_without_ext}_grokResponse.json")
            with open(outputPath, "w", encoding="utf-8") as f:
                json.dump(jsonData, f, indent=4)
            print("✅ JSON saved to outputGrokNewFormat_gpt4o.json")
        else:
            print("❌ No valid JSON object found in response.")
    except Exception as e:
        logging.error(f"Error in OpenAI call: {e}")
        logging.error(traceback.format_exc())

async def process_credit_cards(input_pdf_path: str, processingType: str =  None, processingName: str = None) -> str:
    import socket, platform

    # Set up folders as before (without PDFs dir)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S") + "_" + str(uuid.uuid4())[:8]
    base_dir = "Data1"
    timestamped_dir = os.path.join(base_dir, timestamp)
    input_pdf_dir = os.path.join(timestamped_dir, "inputPDF")
    response_dir = os.path.join(timestamped_dir, "response")
    api_responses_dir = os.path.join(timestamped_dir, "apiResponses")
    for d in [timestamped_dir, input_pdf_dir, response_dir, api_responses_dir]:
        os.makedirs(d, exist_ok=True)

    # Save system info as before...
    
    Config.initialize(
        "config/masterConfig.json",
        section_name = processingType,
        entity_name = processingName
    )
    
    bank_config = Config.load_config()
    print("Bank Config: ",bank_config)
    
    # Copy PDF to input dir
    try:
        shutil.copy2(input_pdf_path, os.path.join(input_pdf_dir, os.path.basename(input_pdf_path)))
    except Exception as e:
        logging.error(f"Error copying PDF: {e}")
        logging.error(traceback.format_exc())
        return None

    copied_pdf_path = os.path.join(input_pdf_dir, os.path.basename(input_pdf_path))

    # Copy processing metadata if it exists
    try:
        upload_dir = os.path.dirname(input_pdf_path)
        metadata_source = os.path.join(upload_dir, "processing_metadata.json")
        if os.path.exists(metadata_source):
            metadata_dest = os.path.join(timestamped_dir, "processing_metadata.json")
            shutil.copy2(metadata_source, metadata_dest)
            logging.info(f"Copied processing metadata to {metadata_dest}")
    except Exception as e:
        logging.warning(f"Could not copy processing metadata: {e}")
    
    # Extract text and tables from the whole PDF
    try:
        extraction_result = await extract_by_aws_textract(copied_pdf_path, api_responses_dir = api_responses_dir)
        if extraction_result.get("status") != "success":
            logging.error("Text extraction failed.")
            return None
        else:
            logging.info("Text extraction successful.")
    except Exception as e:
        logging.error(f"Error in text extraction: {e}")
        logging.error(traceback.format_exc())
        return None

    try:
        openAICall(timestamped_dir)
    except Exception as e:
        logging.error(f"Error in OpenAI call: {e}")
        logging.error(traceback.format_exc())
    
    # logging.info("Calling Excel Generator")
    # try:
    #     creditCardExcel(timestamped_dir)
    #     logging.info("Excel Generated")
    # except Exception as e:
    #     logging.error(f"Error in Excel call: {e}")
    #     logging.error(traceback.format_exc())

    logging.info(f"Processing complete! Results in {timestamped_dir}")
    return os.path.abspath(timestamped_dir)


async def main(input_pdf: str = None):
    """
    Main entry point for the application.

    Args:
        input_pdf: Path to the input PDF file
    """
    # Use the provided input_pdf or default to a sample file
    if input_pdf is None:
        clientName = "XAI"
        input_pdf = r"All Credit Cards\BankOfAmerica\CC # 2095\August 2095.pdf"  # Default path to the input PDF file
        processingType = "Credit Cards"
        processingName = "Bank of America"
        
    Config.initialize(
        "config/masterConfig.json",
        section_name = processingType,
        entity_name = processingName
    )
        
    if not os.path.exists(input_pdf):
        logging.error(f"Input PDF file '{input_pdf}' does not exist")
        return None

    # Process the Credit Card
    start_time = time.time()
    logging.info(f"Starting Credit Card processing for: {input_pdf}")

    results_dir = await process_credit_cards(
        input_pdf,
        processingType = processingType,
        processingName = processingName
    )

    if results_dir:
        processing_time = time.time() - start_time
        logging.info(f"Credit Card processing completed in {processing_time:.2f} seconds")
        logging.info(f"Results saved to: {results_dir}")
    else:
        logging.error("Credit Card processing failed")

    return results_dir


if __name__ == "__main__":
    # Get input PDF path from command line arguments
    if len(sys.argv) > 1:
        input_pdf = sys.argv[1]
        asyncio.run(main(input_pdf))
    else:
        # Run with default PDF path
        asyncio.run(main())
