#!/usr/bin/env python3
"""
Simple Calculator Module

This module provides a Calculator class with basic arithmetic operations
and a command-line interface for interactive calculations.
"""

class Calculator:
    """
    A simple calculator class that provides basic arithmetic operations.
    
    Methods:
        add(a, b): Returns the sum of a and b
        subtract(a, b): Returns the difference of a and b
        multiply(a, b): Returns the product of a and b
        divide(a, b): Returns the quotient of a and b
        power(a, b): Returns a raised to the power of b
        square_root(a): Returns the square root of a
    """
    
    def add(self, a, b):
        """Return the sum of a and b."""
        return a + b
    
    def subtract(self, a, b):
        """Return the difference of a and b."""
        return a - b
    
    def multiply(self, a, b):
        """Return the product of a and b."""
        return a * b
    
    def divide(self, a, b):
        """Return the quotient of a and b."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b
    
    def power(self, a, b):
        """Return a raised to the power of b."""
        return a ** b
    
    def square_root(self, a):
        """Return the square root of a."""
        if a < 0:
            raise ValueError("Cannot calculate square root of a negative number")
        return a ** 0.5


def display_menu():
    """Display the calculator menu options."""
    print("\n===== Meetul's Calculator =====")
    print("1. Addition")
    print("2. Subtraction")
    print("3. Multiplication")
    print("4. Division")
    print("5. Power")
    print("6. Square Root")
    print("7. Exit")
    print("==============================")


def get_number_input(prompt):
    """Get a valid number input from the user."""
    while True:
        try:
            return float(input(prompt))
        except ValueError:
            print("Invalid input. Please enter a number.")


def main():
    """Run the calculator program."""
    calc = Calculator()
    
    while True:
        display_menu()
        choice = input("Enter your choice (1-7): ")
        
        if choice == '7':
            print("Thank you for using Meetul's Calculator. Goodbye!")
            break
        
        if choice == '6':  # Square root only needs one number
            num = get_number_input("Enter a number: ")
            try:
                result = calc.square_root(num)
                print(f"Square root of {num} = {result}")
            except ValueError as e:
                print(f"Error: {e}")
        elif choice in ['1', '2', '3', '4', '5']:
            num1 = get_number_input("Enter first number: ")
            num2 = get_number_input("Enter second number: ")
            
            try:
                if choice == '1':
                    result = calc.add(num1, num2)
                    print(f"{num1} + {num2} = {result}")
                elif choice == '2':
                    result = calc.subtract(num1, num2)
                    print(f"{num1} - {num2} = {result}")
                elif choice == '3':
                    result = calc.multiply(num1, num2)
                    print(f"{num1} × {num2} = {result}")
                elif choice == '4':
                    result = calc.divide(num1, num2)
                    print(f"{num1} ÷ {num2} = {result}")
                elif choice == '5':
                    result = calc.power(num1, num2)
                    print(f"{num1} ^ {num2} = {result}")
            except ValueError as e:
                print(f"Error: {e}")
        else:
            print("Invalid choice. Please enter a number between 1 and 7.")


if __name__ == "__main__":
    print("Welcome to Meetul's Calculator!")
    main()
