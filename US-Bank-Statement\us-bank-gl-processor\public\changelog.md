# Changelog

All notable changes to the US Bank Statement GL Processor will be documented in this file.


## [1.1.0] - 2025-01-14

### Changed By
**Developer:** Meetul Agrawal
**Date:** January 14, 2025

### Changes
- ✅ Added maximum file size support (10 MB limit)
- ✅ Added "Show PDF" functionality between Download PDF and Download Excel buttons
- ✅ Added processing cost warning (Rs. 3 per page) between Select Processing Type and Upload sections
- ✅ Added changelog functionality with version history
- ✅ Enhanced file validation with detailed error messages
- ✅ Improved user experience with better file size information display

### Technical Details
- Enhanced FileUploader component with file size validation
- Added PDF viewing capability in new tab/window
- Implemented warning alert component with red theme styling
- Created changelog infrastructure with markdown support
- Added changelog modal/dialog component

---

## [1.0.0] - 2024-12-01

### Changed By
**Developer:** Accuvelocity Team  
**Date:** December 1, 2024

### Changes
- 🎉 Initial release of US Bank Statement GL Processor
- ✅ PDF file upload and processing functionality
- ✅ Excel file generation and download
- ✅ Dark/Light theme support
- ✅ Processing history panel
- ✅ Multiple bank and credit card support
- ✅ Responsive design for mobile and desktop
- ✅ Professional UI with Material-UI components

### Technical Details
- Built with React 19 and TypeScript
- Material-UI for component library
- Vite for build tooling
- File processing with AWS Textract integration
- OpenAI integration for data processing
- Flask API backend for file processing
