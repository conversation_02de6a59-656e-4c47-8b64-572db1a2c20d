import os
import json

inputDir = r"data\apiResponses\creditInfo"
outputDir = r"data\trueData\creditInfo"

# Ensure the output directory exists
os.makedirs(outputDir, exist_ok=True)

for root, dirs, files in os.walk(inputDir):
    for file in files:
        inputFilePath = os.path.join(root, file)
        inputFileName, file_extension = os.path.splitext(file)
        
        with open(inputFilePath, 'r', encoding='utf-8') as json_file:
            data = json.load(json_file)
        
        strContent = data.get("output", [])[0].get("content", [{"text": "Content not found in the JSON file."}])[0].get("text")
        
        # Define the output file name
        file_name = inputFileName.replace("_gptResponse", "") + "_trueData.json"
        file_path = os.path.join(outputDir, file_name)
        
        # Write data to JSON file
        with open(file_path, 'w', encoding='utf-8') as json_file:
            jsonResponse = json.loads(strContent)
            json.dump(jsonResponse, json_file, indent=4, ensure_ascii=False)

print("Processing complete. Files saved in:", outputDir)


# import os
# import json

# inputDir = r"data\apiResponses\debitInfo"
# outputDir = r"data\trueData\debitInfo"

# os.makedirs(outputDir, exist_ok=True)

# for root, dirs, files in os.walk(inputDir):
#     for file in files:
#         inputFilePath = os.path.join(root, file)
#         inputFileName, file_extension = os.path.splitext(file)

#         with open(inputFilePath, 'r', encoding='utf-8') as json_file:
#             try:
#                 data = json.load(json_file)
#             except json.JSONDecodeError as e:
#                 print(f"Error reading JSON file {inputFilePath}: {e}")
#                 continue  # Skip invalid JSON files
        
#         output_data = data.get("output", [])

#         if len(output_data) < 2:
#             print(f"Warning: 'output' does not have enough elements in {inputFilePath}")
#             continue

#         content_data = output_data[1].get("content", [])

#         if not content_data or not isinstance(content_data, list):
#             print(f"Warning: 'content' is missing or not a list in {inputFilePath}")
#             continue

#         strContent = content_data[0].get("text", "").strip()

#         if not strContent:
#             print(f"Warning: 'text' is missing or empty in {inputFilePath}")
#             continue

#         # Define the output file name
#         file_name = inputFileName.replace("_gptResponse", "") + "_trueData.json"
#         file_path = os.path.join(outputDir, file_name)

#         # Try parsing the JSON content safely
#         try:
#             jsonResponse = json.loads(strContent)
#         except json.JSONDecodeError as e:
#             print(f"Error decoding JSON content in {inputFilePath}: {e}")
#             continue

#         # Write data to JSON file
#         with open(file_path, 'w', encoding='utf-8') as json_file:
#             json.dump(jsonResponse, json_file, indent=4, ensure_ascii=False)

# print("Processing complete. Files saved in:", outputDir)
