# scripts/callApi.py

import openai
import json
import os
import logging
import traceback
import shutil
import asyncio
from openai import OpenAI
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from utils import MloadConfig, MloadSystemPrompt, MloadResponseFormat, MsetupLogging
from ensure import ensure_annotations
from typing import Dict, Any, Union
from AWS_Async3 import extractByAwsTextract
import fitz


# NOTE
# This file is for Parag Traders vendor only
# Assumes PDF to text conversion file is inside the folder of the with same name file + "_Combined.txt" (abc.pdf is inside abc folder with abc_Combined.txt name)

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"


class CExtractByOpenai:
    def __init__(self, strFilePath, strVendorName="1_simpolo") -> None:

        self.m_config: Dict[str, Any] = MloadConfig()

        self.client = OpenAI()
        
        self.m_strFilePath = strFilePath
        self.m_strFolderPath = os.path.dirname(self.m_strFilePath)
        self.m_strFileName = os.path.basename(strFilePath)
        self.m_strFileNameWithoutExtension = os.path.splitext(self.m_strFileName)[0]
        self.m_strVendorName = strVendorName
        self.m_apiResponsePath = self.m_config.get("apiResponsesPath")

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extracts text from a single PDF file.

        :param pdf_path: Path to the PDF file.
        :return: Extracted text as a single string.
        """
        text = ""
        doc = fitz.open(pdf_path)
        # Iterate through all pages
        for page_num in range(doc.page_count):
            page = doc[page_num]
            text += page.get_text() + "\n"  # Append text from each page
        doc.close()
        return text

    
    def MloadConfigurations(self):
        
        self.m_strSystemPrompt = '''
                                I have given conversion of invoice from PDF to text in userprompt. 
                                Step 1 : It should be TAX INVOICE. It is extremely important.
                                Step 2 : If it is TAX INVOICE, Identify and reply with seller name.
                                Step 3 : If it is not TAX INVOICE then reply with "null".
                                '''

        self.m_strResponseFormat = {
                                        "type": "json_schema",
                                        "json_schema": {
                                            "name": "simpolo",
                                            "strict": True,
                                            "schema": {
                                                "type": "object",
                                                "properties": {
                                                    "SellerName": {
                                                        "type": "string",
                                                        "description": "Name of the seller company",
                                                        "enum": [
                                                            "simpolo",
                                                            "nexion",
                                                            "hansgrohe",
                                                            "kohler",
                                                            "toto",
                                                            "geberit",
                                                            "null"
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "SellerName"
                                                ],
                                                "additionalProperties": False
                                            }
                                        }
                                    }
        
        strTextFilePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension, self.m_strFileNameWithoutExtension+TEXT_CONVERSION_SUFFIX+".txt")

        # Extract text with AWS Textraxt if not present
        if not os.path.exists(strTextFilePath):
            asyncio.run(extractByAwsTextract(self.m_strFilePath))

        # Load the user prompt
        with open(strTextFilePath, 'r') as file:
            self.m_strUserContentStructured = file.read()
    
    def McallOpenAiApi(self):

        try:
            print("------------- Started extraction by OpenAI ------------------")
            self.objResponse = self.client.beta.chat.completions.parse(
                    model="gpt-4o-mini-2024-07-18",
                    messages=[
                        {"role": "system", "content": self.m_strSystemPrompt},
                        {"role": "user", "content": str(self.m_strUserContentStructured)}
                    ],
                    response_format=self.m_strResponseFormat,
                    max_tokens=16384,
                    seed=33,
                    temperature=0
                )
            
            print("------------- OpenAI Extraction completed ------------------")
            return self.objResponse
        
        
        except Exception as e:
            print(f"Exeption occured : {e}")
            return None
        
    def MSaveResponse(self):
        
        strResponseFolderPath = os.path.join(self.m_apiResponsePath, self.m_strVendorName)
        os.makedirs(strResponseFolderPath, exist_ok=True)
        strResponseFileName = self.m_strFileNameWithoutExtension + GPT_RESPONSE_SUFFIX + ".json"
        strResponseFilePath = os.path.join(strResponseFolderPath, strResponseFileName)

        with open(strResponseFilePath, "w") as json_file:
            json.dump(self.objResponse.model_dump(), json_file, indent=4)

    def MMoveFile(self):
        
        # Moving pdf file
        strSourcePath = self.m_strFilePath
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        os.makedirs(strDestinationPath, exist_ok=True)
        shutil.move(strSourcePath, strDestinationPath)

        # moving folder of pdf file which has text and tables data
        strSourcePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension)
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        shutil.move(strSourcePath, strDestinationPath)

        pass



class CProcessDocument:

    def __init__(self, strVendorName="1_simpolo") -> None:
        self.m_dictConfig = MloadConfig()      # loads config in dict
        self.strVendorName = strVendorName


    def MprocessDocument(self, strFilePath) -> None:

        print(f"Processing {strFilePath}")
        objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName=self.strVendorName)
        strFileName = os.path.basename(strFilePath)
        strFileNameWithoutExtension = os.path.splitext(strFileName)[0]
        objCExtractByOpenai.MloadConfigurations()
        objCExtractByOpenai.McallOpenAiApi()
        objCExtractByOpenai.MSaveResponse()
        objCExtractByOpenai.MMoveFile()




     
    def MprocessAllDocuments(self, iFilesToProcess=1) -> None:

        strFolderPathToProcess = os.path.join(self.m_dictConfig.get("inputDataPath"), self.strVendorName)
        intTotalFilesProcessed = 0

        listFiles = os.listdir(strFolderPathToProcess)
        with ThreadPoolExecutor() as executor:
            futures = []
            for file in listFiles:
                if file.lower().endswith(".pdf"):
                    strFilePath = os.path.join(strFolderPathToProcess, file)
                    futures.append(executor.submit(self.MprocessDocument, strFilePath))
            for future in as_completed(futures):
                try:
                    future.result()
                    intTotalFilesProcessed += 1
                    if intTotalFilesProcessed >= iFilesToProcess:
                        break
                except Exception as e:
                    logging.error(e)


if __name__ == "__main__":

    # strFilePath = r"data\inputData\1_simpolo\2411007949.PDF"
    # strVendorName = "1_simpolo"
    # objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName)
    # objCExtractByOpenai.MloadConfigurations()
    objCProcessDocument = CProcessDocument(strVendorName="1_simpolo")    
    print("Object Created....")
    # 1_simpolo
    objCProcessDocument.MprocessAllDocuments(iFilesToProcess=1)