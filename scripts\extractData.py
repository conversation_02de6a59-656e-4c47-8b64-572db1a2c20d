# scripts/callApi.py

import openai
import json
import os
import logging
import traceback
import shutil
import asyncio
from openai import OpenAI
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from utils import MloadConfig, MloadSystemPrompt, MloadResponseFormat, MsetupLogging
from ensure import ensure_annotations
from typing import Dict, Any, Union
from AWS_Async import extractByAwsTextract


# NOTE
# This file is for Parag Traders vendor only
# Assumes PDF to text conversion file is inside the folder of the with same name file + "_Combined.txt" (abc.pdf is inside abc folder with abc_Combined.txt name)

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"


class CExtractByOpenai:
    def __init__(self, strFilePath, strVendorName) -> None:

        self.m_config: Dict[str, Any] = MloadConfig()

        self.client = OpenAI()
        
        self.m_strFilePath = strFilePath
        self.m_strFolderPath = os.path.dirname(self.m_strFilePath)
        self.m_strFileName = os.path.basename(strFilePath)
        self.m_strFileNameWithoutExtension = os.path.splitext(self.m_strFileName)[0]
        self.m_strVendorName = strVendorName

        self.m_strSystemPromptOfAllVendors = self.m_config.get("systemPromptFilePath")
        self.m_strResponseFormatFilePath = self.m_config.get("responseFormatFilePath")

        self.m_apiResponsePath = self.m_config.get("apiResponsesPath")
        pass
    
    def MloadConfigurations(self):
        
        strTextFilePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension, self.m_strFileNameWithoutExtension+TEXT_CONVERSION_SUFFIX+".txt")

        # Extract text with AWS Textraxt if not present
        if not os.path.exists(strTextFilePath):
            asyncio.run(extractByAwsTextract(self.m_strFilePath))

        # Load the user prompt
        with open(strTextFilePath, 'r') as file:
            self.m_strUserContentStructured = file.read()

        self.m_strSystemPrompt = self.m_strSystemPromptOfAllVendors

        # Load the response format
        with open(self.m_strResponseFormatFilePath, 'r') as file:
            self.m_strResponseFormatOfAllVendors = json.load(file)

        self.m_strResponseFormat = self.m_strResponseFormatOfAllVendors[self.m_strVendorName]
    
    def McallOpenAiApi(self):

        try:
            print("------------- Started extraction by OpenAI ------------------")
            self.objResponse = self.client.beta.chat.completions.parse(
                    model="gpt-4o-2024-08-06",
                    messages=[
                        {"role": "system", "content": self.m_strSystemPrompt},
                        {"role": "user", "content": str(self.m_strUserContentStructured)}
                    ],
                    response_format=self.m_strResponseFormat,
                    max_tokens=16384,
                    seed=33,
                    temperature=0
                )
            
            print("------------- OpenAI Extraction completed ------------------")
            return self.objResponse
        
        
        except Exception as e:
            print(f"Exeption occured : {e}")
            return None
        
    def MSaveResponse(self):
        
        strResponseFolderPath = os.path.join(self.m_apiResponsePath, self.m_strVendorName)
        os.makedirs(strResponseFolderPath, exist_ok=True)
        strResponseFileName = self.m_strFileNameWithoutExtension + GPT_RESPONSE_SUFFIX + ".json"
        strResponseFilePath = os.path.join(strResponseFolderPath, strResponseFileName)

        print("file  save at : {strResponseFilePath}" )
        
        with open(strResponseFilePath, "w") as json_file:
            json.dump(self.objResponse.model_dump(), json_file, indent=4)

    def MMoveFile(self):
        
        # Moving pdf file
        strSourcePath = self.m_strFilePath
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        os.makedirs(strDestinationPath, exist_ok=True)
        shutil.move(strSourcePath, strDestinationPath)

        # moving folder of pdf file which has text and tables data
        strSourcePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension)
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        shutil.move(strSourcePath, strDestinationPath)




class CProcessDocument:

    def __init__(self, strVendorName="12_GEBERIT") -> None:
        self.m_dictConfig = MloadConfig()      # loads config in dict
        self.strVendorName = strVendorName


    def MprocessDocument(self, strFilePath, strVendorName) -> None:

        print(f"Processing {strFilePath}")
        # objCExtractByOpenai = CExtractByOpenai(strFilePath)
        strFileName = os.path.basename(strFilePath)
        strFileNameWithoutExtension = os.path.splitext(strFileName)[0]
        objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName)
        print(f"OPen Ai Object")
        objCExtractByOpenai.MloadConfigurations()
        print(f"Load Config")
        objCExtractByOpenai.McallOpenAiApi()
        print(f"Call Open Ai")
        objCExtractByOpenai.MSaveResponse()
        print(f"Save Response")
        objCExtractByOpenai.MMoveFile()
        print(f"Move File")


        


    def MprocessAllDocuments(self, iFilesToProcess=1) -> None:

        # strFolderPathToProcess = os.path.join(self.m_dictConfig.get("inputDataPath"), self.strVendorName)
        strFolderPathToProcess = os.path.join("data/inputData", self.strVendorName)
        intTotalFilesProcessed = 0

        print(f"Inside MprocessAllDocuments {strFolderPathToProcess}")
        
        listFiles = os.listdir(strFolderPathToProcess)
        
        for file in listFiles:
            print(f"Inside file list ")
            if file.endswith(".pdf") or file.endswith(".PDF"):
                strFilePath = os.path.join(strFolderPathToProcess, file)
                self.MprocessDocument(strFilePath=strFilePath,strVendorName=self.strVendorName)
                intTotalFilesProcessed += 1
                if intTotalFilesProcessed >= iFilesToProcess:
                    break


if __name__ == "__main__":

    # strFilePath = r"data\inputData\1_simpolo\2411007949.PDF"
    # strVendorName = "1_simpolo"
    # objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName)
    # objCExtractByOpenai.MloadConfigurations()
    print("Start From Here")
    objCProcessDocument = CProcessDocument(strVendorName="1_simpolo")
    print("Object Created")
    objCProcessDocument.MprocessAllDocuments(iFilesToProcess=1)
