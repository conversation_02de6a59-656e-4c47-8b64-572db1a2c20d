import asyncio
import aioboto3
import aiofiles
import json
import sys
import logging
from botocore.exceptions import ClientError
import uuid
import csv  # Added import for CSV handling
import os  # Added import for file path handling
from openai import OpenAI
from io import StringIO

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)  # Logs to stdout
    ]
)

strSystemPromptStructured_1 = '''You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: 

Step 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. 

Step 2: Give output in the given structure:

Step 3: Analyze UserContent completely that is given in the following JSON structure 
{
    "Text" : {
        [ActualPageNumber]: [('[ActualText]', [PageNumber], [x1], [y1], [x2], [y2]),...],...
    }
    
Tables
Heading1, Heading2, ... HeadingN
Cell1, Cell2,..., CellN

Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text. 

Step 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. 

Step 5: Find relevant information from the invoice and fill out the required output JSON file. For tables, consider "Tables" key-value. If something is not found in the invoice then keep the respective value of the output as ''. 

Step 6: Check again for missing any line item in LineItemTable and strictly include all line items from all pages in the output JSON. DO NOT ASSUME ANYTHING.

'''

response_format_1 = {
    "type": "json_schema",
    "json_schema": {
        "name": "simpolo",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                # ... (JSON schema properties as defined in your original code)
                # For brevity, not repeating the entire JSON schema here
            },
            "required": [
                # ... (List of required properties as defined in your original code)
            ],
            "additionalProperties": False
        }
    }
}


def filter_text_data_by_tables(text_data, tables):
    """
    Removes any text lines from text_data that also appear in the tables.
    
    :param text_data: Dictionary with page numbers as keys and list of text lines as values.
                      Each text line is expected to be [Text, X1, Y1, X2, Y2].
    :param tables: List of tables (each table is a list of rows; each row is a list of cell texts).
    :return: Filtered text_data dictionary with text lines present in tables removed.
    """
    # Create a set of normalized table cell texts.
    table_text_set = set()
    for table in tables:
        for row in table:
            for cell in row:
                norm = cell.strip().lower()
                if norm:
                    table_text_set.add(norm)
    
    filtered_text_data = {}
    for page, lines in text_data.items():
        filtered_lines = []
        for line in lines:
            norm_text = line[0].strip().lower()
            if norm_text not in table_text_set:
                filtered_lines.append(line)
        if filtered_lines:
            filtered_text_data[page] = filtered_lines
    return filtered_text_data


async def upload_file_to_s3(s3_client, file_path, bucket_name, object_name=None):
    """
    Uploads a file to an S3 bucket.

    :param s3_client: aioboto3 S3 client.
    :param file_path: Path to the local file.
    :param bucket_name: S3 bucket name.
    :param object_name: S3 object name. If not specified, file_path is used.
    :return: S3 object key.
    """
    if object_name is None:
        object_name = os.path.basename(file_path)
    
    try:
        async with aiofiles.open(file_path, 'rb') as f:
            data = await f.read()
        await s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
        logging.info(f"Uploaded {file_path} to s3://{bucket_name}/{object_name}")
        return object_name
    except ClientError as e:
        logging.error(f"Failed to upload file to S3: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Unexpected error uploading file to S3: {e}")
        sys.exit(1)

async def start_document_analysis(textract_client, bucket_name, object_name, job_tag="job", notification_channel=None):
    """
    Starts asynchronous document analysis with Textract.

    :param textract_client: aioboto3 Textract client.
    :param bucket_name: S3 bucket name where the document is stored.
    :param object_name: S3 object name of the document.
    :param job_tag: Identifier for the job.
    :param notification_channel: (Optional) SNS topic ARN for notifications.
    :return: Job ID
    """
    try:
        params = {
            'DocumentLocation': {
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': object_name
                }
            },
            'FeatureTypes': ['TABLES'],
            'JobTag': job_tag
        }
        if notification_channel:
            params['NotificationChannel'] = notification_channel

        response = await textract_client.start_document_analysis(**params)
        job_id = response['JobId']
        logging.info(f"Started document analysis job with Job ID: {job_id}")
        return job_id
    except ClientError as e:
        logging.error(f"Error starting document analysis: {e}")
        sys.exit(1)

async def get_document_analysis(textract_client, job_id):
    """
    Retrieves the results of an asynchronous document analysis job.

    :param textract_client: aioboto3 Textract client.
    :param job_id: ID of the Textract job.
    :return: List of blocks from Textract response.
    """
    try:
        while True:
            response = await textract_client.get_document_analysis(JobId=job_id)
            status = response['JobStatus']
            logging.info(f"Job status: {status}")

            if status in ['SUCCEEDED', 'FAILED']:
                if status == 'SUCCEEDED':
                    logging.info("Document analysis succeeded.")
                    blocks = response.get('Blocks', [])
                    # Handle pagination if NextToken is present
                    next_token = response.get('NextToken', None)
                    while next_token:
                        logging.info("Retrieving next page of results...")
                        response = await textract_client.get_document_analysis(JobId=job_id, NextToken=next_token)
                        blocks.extend(response.get('Blocks', []))
                        next_token = response.get('NextToken', None)
                    return blocks
                else:
                    logging.error("Document analysis failed.")
                    sys.exit(1)
            await asyncio.sleep(5)  # Wait before polling again
    except ClientError as e:
        logging.error(f"Error getting document analysis: {e}")
        sys.exit(1)

def extract_tables_from_blocks(blocks):
    """
    Extracts tables from Textract response blocks.

    :param blocks: List of blocks from Textract response.
    :return: List of tables, each table is a list of rows, each row is a list of cell texts.
    """
    tables = []
    table_blocks = [block for block in blocks if block['BlockType'] == 'TABLE']

    for table in table_blocks:
        cell_blocks = []

        # Extract CELL blocks related to the current table
        relationships = table.get('Relationships', [])
        for rel in relationships:
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    cell = next((b for b in blocks if b['Id'] == child_id and b['BlockType'] == 'CELL'), None)
                    if cell:
                        cell_blocks.append(cell)

        if not cell_blocks:
            continue

        # Sort cells by row and column
        sorted_cells = sorted(cell_blocks, key=lambda x: (x.get('RowIndex', 0), x.get('ColumnIndex', 0)))

        # Determine the number of rows and columns
        max_row = max(cell.get('RowIndex', 0) for cell in sorted_cells)
        max_col = max(cell.get('ColumnIndex', 0) for cell in sorted_cells)

        # Initialize table structure
        table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

        for cell in sorted_cells:
            row = cell.get('RowIndex', 0) - 1  # Zero-based index
            col = cell.get('ColumnIndex', 0) - 1  # Zero-based index
            text = get_text_for_block(blocks, cell)
            if 0 <= row < max_row and 0 <= col < max_col:
                table_data[row][col] = text
            else:
                logging.warning(f"Cell position out of bounds. Row: {row+1}, Column: {col+1}")

        tables.append(table_data)

    return tables

def get_text_for_block(blocks, cell_block):
    """
    Retrieves the text associated with a cell block.

    :param blocks: List of all blocks.
    :param cell_block: The cell block for which text is to be retrieved.
    :return: Concatenated text within the cell.
    """
    text = []
    relationships = cell_block.get('Relationships', [])
    for rel in relationships:
        if rel['Type'] == 'CHILD':
            for child_id in rel['Ids']:
                child = next((b for b in blocks if b['Id'] == child_id), None)
                if child:
                    if child['BlockType'] == 'WORD':
                        text.append(child.get('Text', ''))
                    elif child['BlockType'] == 'SELECTION_ELEMENT' and child.get('SelectionStatus') == 'SELECTED':
                        text.append("X")
    return ' '.join(text)

def get_text_without_tables(blocks):
    """
    Extracts text from Textract response blocks, excluding tables.

    :param blocks: List of blocks from Textract response.
    :return: Dictionary containing extracted text information.
    """
    # Create a mapping from block Id to block for quick lookup
    id_to_block = {block['Id']: block for block in blocks}

    # Collect all WORD Ids that are part of cells
    word_ids_in_cells = set()
    for block in blocks:
        if block['BlockType'] == 'CELL':
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        word_ids_in_cells.update(relationship['Ids'])

    # Map LINE block Ids to their respective page numbers
    line_id_to_page = {}
    page_number = 1  # Assuming page numbers start from 1
    for block in blocks:
        if block['BlockType'] == 'PAGE':
            page_id = block['Id']
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        for child_id in relationship['Ids']:
                            child_block = id_to_block.get(child_id)
                            if child_block and child_block['BlockType'] == 'LINE':
                                line_id_to_page[child_id] = page_number
            page_number += 1  # Increment page number for the next PAGE block

    # Collect LINE blocks that are not part of any table (CELL)
    non_table_lines = []
    for block in blocks:
        if block['BlockType'] == 'LINE':
            is_in_table = False
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        # Check if any of the LINE's child WORD Ids are in word_ids_in_cells
                        if any(child_id in word_ids_in_cells for child_id in relationship['Ids']):
                            is_in_table = True
                            break
            if not is_in_table:
                non_table_lines.append(block)

    # Prepare the output in the desired format
    output = {}
    for line in non_table_lines:
        text = line['Text']
        bbox = line['Geometry']['BoundingBox']
        x1 = round(bbox['Left'], 5)
        y1 = round(bbox['Top'], 5)
        x2 = round(x1 + bbox['Width'], 5)
        y2 = round(y1 + bbox['Height'], 5)
        line_id = line['Id']
        page_num = line_id_to_page.get(line_id, 1)  # Default to page 1 if not found

        if page_num not in output:
            output[page_num] = []
        output[page_num].append([
            text,
            x1,
            y1,
            x2,
            y2
        ])

    return output

def get_text_without_tables_csv(blocks, filtered_text_data=None):
    """
    Extracts text (excluding tables) from Textract blocks and returns a list
    of CSV-formatted strings, where each string is in the format:
    
    Page No.,Text,X1,Y1,X2,Y2
    
    :param blocks: List of blocks from Textract response.
    :return: List of CSV formatted strings.
    """
    # Get the dictionary output from your existing function.
    # The expected format is:
    # {
    #     "1": [
    #         [ "Satyam traders", 0.25725, 0.11089, 0.48435, 0.13145 ],
    #         [ "GST No. 24AEAPS1439MIZDX", 0.25817, 0.13548, 0.578, 0.14716 ],
    #         ...
    #     ],
    #     "2": [...],
    #     ...
    # }
   # If filtered_text_data is not provided, get the unfiltered text_data.
    if filtered_text_data is None:
        text_data = get_text_without_tables(blocks)
    else:
        text_data = filtered_text_data
    
    csv_rows = []
    # Iterate over each page and its associated lines.
    for page, lines in text_data.items():
        for line in lines:
            # Each line is expected to be a list: [Text, X1, Y1, X2, Y2]
            # Ensure the page number is converted to an integer if needed.
            try:
                page_num = int(page)
            except ValueError:
                page_num = page
            # Format the CSV row. Adjust formatting (like rounding) as necessary.
            csv_row = f"{page_num},{line[0]},{line[1]},{line[2]},{line[3]},{line[4]}"
            csv_rows.append(csv_row)
    
    return csv_rows





async def process_document(local_pdf_path, bucket_name, object_name=None, job_tag="ExtractTablesJob", notification_channel=None, 
                           base_filename=None):
    """
    Main processing function to handle the document analysis.

    :param local_pdf_path: Path to the local PDF file.
    :param bucket_name: S3 bucket name.
    :param object_name: S3 object name. If not specified, a unique name will be generated.
    :param job_tag: Identifier for the job.
    :param notification_channel: (Optional) SNS topic ARN for notifications.
    :param base_filename: Base name for output files.
    """
    # Initialize aioboto3 Session
    session = aioboto3.Session()

    # Derive base filename if not provided
    if base_filename is None:
        base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    # Determine the directory of the local PDF file
    file_dir = os.path.dirname(os.path.abspath(local_pdf_path))

    # Define the output folder path (same as base_filename inside file_dir)
    output_folder = os.path.join(file_dir, base_filename)

    # Create the output folder if it doesn't exist
    try:
        os.makedirs(output_folder, exist_ok=True)
        logging.info(f"Output folder created at: {output_folder}")
    except Exception as e:
        logging.error(f"Error creating output folder: {e}")
        sys.exit(1)

    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Step 1: Upload PDF to S3
        if not object_name:
            object_name = f"textract-input/{uuid.uuid4()}-{os.path.basename(local_pdf_path)}"
        s3_key = await upload_file_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Step 2: Start Document Analysis with Textract
        logging.info("Starting document analysis with Textract...")
        job_id = await start_document_analysis(textract_client, bucket_name, s3_key, job_tag, notification_channel)

        # Step 3: Get Document Analysis Results
        logging.info("Polling for document analysis results...")
        blocks = await get_document_analysis(textract_client, job_id)

        # Step 4: Extract Tables
        logging.info("Extracting tables from results...")
        tables = extract_tables_from_blocks(blocks)
        logging.info(f"Found {len(tables)} table(s).")

        # Step 5: Extract Text Without Tables
        logging.info("Extracting text without tables from results...")
        text_content = get_text_without_tables(blocks)
        filtered_text_content = filter_text_data_by_tables(text_content, tables)
        #---------------------Sample Code--------------------
        # Assuming `blocks` contains your Textract response blocks:
        csv_lines = get_text_without_tables_csv(blocks, filtered_text_content)

        # Option 1: Print each CSV line to the console
        for line in csv_lines:
            print(line)

        # Option 2: Write the CSV lines to a file
        output_csv_filename = os.path.join(output_folder, f"{base_filename}_ExtractedText.txt")
        with open(output_csv_filename, 'w', newline='', encoding='utf-8') as file:
            file.write("Page No., Text, X1, Y1, X2, Y2\n")
            for line in csv_lines:
                file.write(line + "\n")
        logging.info(f"CSV file with text extracted (without tables) saved to {output_csv_filename}.")

        #---------------------Sample Code--------------------
        text_json_filename = os.path.join(output_folder, f"{base_filename}_Text.json")
        try:
            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(filtered_text_content, ensure_ascii=False, indent=4))
            logging.info(f"Extracted text saved to {text_json_filename}.")
        except Exception as e:
            logging.error(f"Error writing to JSON file: {e}")
            sys.exit(1)

        # Step 6: Save Tables to CSV
        logging.info("Saving extracted tables to CSV format...")
        table_csv_filenames = []
        try:
            for idx, table in enumerate(tables, start=1):
                csv_filename = os.path.join(output_folder, f"{base_filename}_Table{idx}.csv")
                with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(table)
                logging.info(f"Extracted table {idx} saved to {csv_filename}.")
                table_csv_filenames.append(csv_filename)
        except Exception as e:
            logging.error(f"Error writing to CSV file: {e}")
            sys.exit(1)

        # Step 7: Create Combined Text File
        logging.info("Creating combined text file...")
        combined_txt_filename = os.path.join(output_folder, f"{base_filename}_strUserPrompt.txt")
        try:
            async with aiofiles.open(combined_txt_filename, 'w', encoding='utf-8') as combined_file:
                # Write Text section
                await combined_file.write("Text\n")
                await combined_file.write(json.dumps(filtered_text_content, ensure_ascii=False, indent=4))
                await combined_file.write("\n\n")

                # Write Tables
                for idx, csv_filename in enumerate(table_csv_filenames, start=1):
                    await combined_file.write(f"Table-{idx}\n")
                    
                    # Open the CSV file for reading
                    with open(csv_filename, 'r', encoding='utf-8', newline='') as csvfile:
                        reader = csv.reader(csvfile)
                        
                        # Use StringIO to accumulate the modified CSV data in memory
                        output = StringIO()
                        writer = csv.writer(output)
                        
                        # Iterate over each row in the CSV
                        for row in reader:
                            # Replace empty cells with a single space
                            new_row = [cell if cell.strip() != '' else '0' for cell in row]
                            writer.writerow(new_row)
                        
                        # Retrieve the modified CSV content as a string
                        table_data = output.getvalue()
                    
                    # Write the modified CSV data to the combined file
                    await combined_file.write(table_data)
                    await combined_file.write("\n\n")
                    #----------------------------------Sample Code-----------------
            async with aiofiles.open(output_csv_filename, 'a', encoding='utf-8') as combined_file:
                await combined_file.write("\n\n")
                # Write Tables
                for idx, csv_filename in enumerate(table_csv_filenames, start=1):
                    await combined_file.write(f"Table-{idx}\n")
                    
                    # Open the CSV file for reading
                    with open(csv_filename, 'r', encoding='utf-8', newline='') as csvfile:
                        reader = csv.reader(csvfile)
                        
                        # Use StringIO to accumulate the modified CSV data in memory
                        output = StringIO()
                        writer = csv.writer(output)
                        
                        # Iterate over each row in the CSV
                        for row in reader:
                            # Replace empty cells with a single space
                            new_row = [cell if cell.strip() != '' else '0' for cell in row]
                            writer.writerow(new_row)
                        
                        # Retrieve the modified CSV content as a string
                        table_data = output.getvalue()
                    
                    # Write the modified CSV data to the combined file
                    await combined_file.write(table_data)
                    await combined_file.write("\n\n")
                    #----------------------------------Sample Code-----------------
            logging.info(f"Combined text file created: {combined_txt_filename}.")
        except Exception as e:
            logging.error(f"Error writing to combined text file: {e}")
            sys.exit(1)

        # Step 8: Save AWS Textract Blocks
        logging.info("Saving AWS Textract blocks to JSON file...")
        blocks_json_filename = os.path.join(output_folder, f"{base_filename}_AWS_Object.json")
        try:
            async with aiofiles.open(blocks_json_filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(blocks, ensure_ascii=False, indent=4))
            logging.info(f"AWS Textract blocks saved to {blocks_json_filename}.")
        except Exception as e:
            logging.error(f"Error writing blocks JSON file: {e}")
            sys.exit(1)

        logging.info("AWS text & table extraction process completed successfully.")
        
        return blocks



async def extractByAwsTextract(local_pdf_path):
    # Configuration
    # local_pdf_path = r'DhruvinTest/Simpolo/2431005551.PDF'  # Replace with your PDF path
    local_pdf_path = local_pdf_path
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name, else a unique name is generated
    job_tag = "ExtractTablesJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    # Derive base filename from the input PDF file name
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    dictExtractedData = await process_document(local_pdf_path, bucket_name, object_name, job_tag, notification_channel, base_filename)

if __name__ == "__main__":
    pass
    # import os
    # strFolderName = r"H:\Customers\Parag Traders\mainProjectParagTraders\data\inputData\1_simpolo\processed"
    # lstPdf = os.listdir(strFolderName)
    # i = 0
    # for strName in lstPdf:
    #     print(f"\n Currently Running : {strName} \n")
    #     if strName.endswith(".pdf") or  strName.endswith(".PDF"):
    #         if strName != "2411007949.PDF":
    #             asyncio.run(main(strFolderName+os.sep+strName))

    # asyncio.run(extractByAwsTextract(r"H:\Customers\Parag Traders\mainProjectParagTraders\data\inputData\1_simpolo\2431000453.PDF"))