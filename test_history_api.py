#!/usr/bin/env python3
"""
Test script to check the history API functionality
"""

import os
import sys
import json
from datetime import datetime

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_history_logic():
    """Test the history logic without running the full Flask server"""
    try:
        history = []
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        
        if not os.path.exists(data_dir):
            print("Data1 directory not found")
            return
            
        print(f"Checking Data1 directory: {data_dir}")
        
        # List all timestamped directories in Data1
        for timestamp_dir in sorted(os.listdir(data_dir), reverse=True):
            timestamp_path = os.path.join(data_dir, timestamp_dir)
            
            # Skip if not a directory
            if not os.path.isdir(timestamp_path):
                continue
                
            print(f"Processing directory: {timestamp_dir}")
            
            # Check for input PDF
            input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
            if not os.path.exists(input_pdf_dir):
                print(f"  No inputPDF directory found")
                continue
                
            # Get the PDF file name
            pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
            if not pdf_files:
                print(f"  No PDF files found")
                continue
                
            print(f"  Found PDF: {pdf_files[0]}")
            
            # Try to read processing metadata
            processing_type = None
            processing_name = None
            metadata_path = os.path.join(timestamp_path, "processing_metadata.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                        processing_type = metadata.get('processingType')
                        processing_name = metadata.get('processingName')
                        print(f"  Found metadata - Type: {processing_type}, Name: {processing_name}")
                except Exception as e:
                    print(f"  Could not read metadata: {e}")
            else:
                print(f"  No metadata file found at: {metadata_path}")
                
            # Check if Excel file was generated
            excel_file_path = None
            output_dir = os.path.join(timestamp_path, "output")
            if os.path.exists(output_dir):
                excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
                if excel_files:
                    excel_file_path = os.path.join(output_dir, excel_files[0])
                    print(f"  Found Excel: {excel_files[0]}")
                    
            # Get file size
            pdf_path = os.path.join(input_pdf_dir, pdf_files[0])
            file_size = os.path.getsize(pdf_path)
            file_size_str = f"{file_size / 1024 / 1024:.2f} MB" if file_size > 1024 * 1024 else f"{file_size / 1024:.0f} KB"
            
            # Parse timestamp from directory name
            timestamp = timestamp_dir  # Default fallback
            try:
                timestamp_parts = timestamp_dir.split('_')
                if len(timestamp_parts) >= 2:
                    date_part = timestamp_parts[0]
                    time_part = timestamp_parts[1]
                    
                    year = int(date_part[0:4])
                    month = int(date_part[4:6])
                    day = int(date_part[6:8])
                    hour = int(time_part[0:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])
                    
                    timestamp = f"{year:04d}-{month:02d}-{day:02d}T{hour:02d}:{minute:02d}:{second:02d}"
            except (ValueError, IndexError):
                pass
                
            # Add to history
            history_item = {
                "id": timestamp_dir,
                "fileName": pdf_files[0],
                "uploadDate": timestamp,
                "fileSize": file_size_str,
                "pageCount": None,  # Would need PyPDF2 for this
                "excelPath": excel_file_path,
                "processingType": processing_type,
                "processingName": processing_name
            }
            
            history.append(history_item)
            print(f"  Added to history: {history_item}")
            print()
            
        print(f"Total history items: {len(history)}")
        return history
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_history_logic()
