import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { Close, History, Person, CalendarToday, CheckCircle, Celebration } from '@mui/icons-material';

interface ChangelogModalProps {
  open: boolean;
  onClose: () => void;
}

interface ChangelogEntry {
  version: string;
  date: string;
  author: string;
  changes: string[];
  technicalDetails: string[];
  isInitialRelease?: boolean;
}

const ChangelogModal: React.FC<ChangelogModalProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  const [changelogData, setChangelogData] = useState<ChangelogEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open) {
      fetchChangelog();
    }
  }, [open]);

  const fetchChangelog = async () => {
    try {
      setLoading(true);
      const response = await fetch('/changelog.md');
      const text = await response.text();
      const parsed = parseChangelog(text);
      setChangelogData(parsed);
    } catch (error) {
      console.error('Error fetching changelog:', error);
      // Fallback data
      setChangelogData([
        {
          version: '1.1.0',
          date: 'January 14, 2025',
          author: 'Pavan Kumar',
          changes: [
            'Added maximum file size support (10 MB limit)',
            'Added "Show PDF" functionality between Download PDF and Download Excel buttons',
            'Added processing cost warning (Rs. 3 per page) between Select Processing Type and Upload sections',
            'Added changelog functionality with version history',
            'Enhanced file validation with detailed error messages',
            'Improved user experience with better file size information display'
          ],
          technicalDetails: [
            'Enhanced FileUploader component with file size validation',
            'Added PDF viewing capability in new tab/window',
            'Implemented warning alert component with red theme styling',
            'Created changelog infrastructure with markdown support',
            'Added changelog modal/dialog component'
          ]
        },
        {
          version: '1.0.0',
          date: 'December 1, 2024',
          author: 'Accuvelocity Team',
          changes: [
            'Initial release of US Bank Statement GL Processor',
            'PDF file upload and processing functionality',
            'Excel file generation and download',
            'Dark/Light theme support',
            'Processing history panel',
            'Multiple bank and credit card support',
            'Responsive design for mobile and desktop',
            'Professional UI with Material-UI components'
          ],
          technicalDetails: [
            'Built with React 19 and TypeScript',
            'Material-UI for component library',
            'Vite for build tooling',
            'File processing with AWS Textract integration',
            'OpenAI integration for data processing',
            'Flask API backend for file processing'
          ],
          isInitialRelease: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const parseChangelog = (text: string): ChangelogEntry[] => {
    // Simple markdown parser for changelog format
    const entries: ChangelogEntry[] = [];
    const lines = text.split('\n');
    let currentEntry: Partial<ChangelogEntry> | null = null;
    let currentSection = '';

    for (const line of lines) {
      if (line.startsWith('## [')) {
        // Save previous entry
        if (currentEntry && currentEntry.version) {
          entries.push(currentEntry as ChangelogEntry);
        }
        
        // Start new entry
        const versionMatch = line.match(/\[([^\]]+)\]/);
        const dateMatch = line.match(/- (.+)$/);
        currentEntry = {
          version: versionMatch ? versionMatch[1] : '',
          date: dateMatch ? dateMatch[1] : '',
          author: '',
          changes: [],
          technicalDetails: []
        };
      } else if (line.includes('**Developer:**') || line.includes('**Changed By**')) {
        const authorMatch = line.match(/\*\*(?:Developer|Changed By):\*\*\s*(.+)/);
        if (currentEntry && authorMatch) {
          currentEntry.author = authorMatch[1];
        }
      } else if (line.startsWith('### Changes')) {
        currentSection = 'changes';
      } else if (line.startsWith('### Technical Details')) {
        currentSection = 'technical';
      } else if (line.startsWith('- ') && currentEntry) {
        const cleanLine = line.replace(/^- /, '').replace(/[✅🎉]/g, '').trim();
        if (currentSection === 'changes') {
          currentEntry.changes!.push(cleanLine);
        } else if (currentSection === 'technical') {
          currentEntry.technicalDetails!.push(cleanLine);
        }
      }
    }

    // Add last entry
    if (currentEntry && currentEntry.version) {
      entries.push(currentEntry as ChangelogEntry);
    }

    return entries;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1,
          background: (theme) =>
            theme.palette.mode === 'light'
              ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
              : `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <History />
          <Typography variant="h6" component="div">
            Changelog
          </Typography>
        </Box>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {loading ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography>Loading changelog...</Typography>
          </Box>
        ) : (
          <Box sx={{ p: 3 }}>
            {changelogData.map((entry, index) => (
              <Box key={entry.version} sx={{ mb: index < changelogData.length - 1 ? 4 : 0 }}>
                {/* Version Header */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip
                    label={`v${entry.version}`}
                    color={entry.isInitialRelease ? 'secondary' : 'primary'}
                    icon={entry.isInitialRelease ? <Celebration /> : <CheckCircle />}
                    sx={{ fontWeight: 600 }}
                  />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <Person fontSize="small" />
                    <Typography variant="body2">{entry.author}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <CalendarToday fontSize="small" />
                    <Typography variant="body2">{entry.date}</Typography>
                  </Box>
                </Box>

                {/* Changes */}
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                  Changes
                </Typography>
                <List dense sx={{ mb: 2 }}>
                  {entry.changes.map((change, changeIndex) => (
                    <ListItem key={changeIndex} sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <CheckCircle color="success" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={change} />
                    </ListItem>
                  ))}
                </List>

                {/* Technical Details */}
                {entry.technicalDetails.length > 0 && (
                  <>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                      Technical Details
                    </Typography>
                    <List dense sx={{ mb: 2 }}>
                      {entry.technicalDetails.map((detail, detailIndex) => (
                        <ListItem key={detailIndex} sx={{ py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircle color="info" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={detail}
                            sx={{ '& .MuiListItemText-primary': { fontSize: '0.875rem' } }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}

                {index < changelogData.length - 1 && <Divider sx={{ mt: 2 }} />}
              </Box>
            ))}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, pt: 1 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ChangelogModal;
