import React, { useState, useEffect } from 'react';
import {
  Dialog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Typography,
  Box,
  IconButton,
  useTheme,
  useMediaQuery,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import { Close, BugReport, Refresh, FilterList } from '@mui/icons-material';

interface LogsModalProps {
  open: boolean;
  onClose: () => void;
}

interface LogEntry {
  status: string;
  user_type: string;
  timestamp: string;
  date: string;
  time: string;
  username: string;
  message: string;
  traceback?: string;
}

interface LogsResponse {
  logs: LogEntry[];
  total: number;
  filters: {
    start_date?: string;
    end_date?: string;
    status?: string;
    limit: number;
  };
}

const LogsModal: React.FC<LogsModalProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    status: '',
    limit: 1000
  });

  useEffect(() => {
    if (open) {
      fetchLogs();
    }
  }, [open]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (filters.startDate) params.append('start_date', filters.startDate);
      if (filters.endDate) params.append('end_date', filters.endDate);
      if (filters.status) params.append('status', filters.status);
      params.append('limit', filters.limit.toString());
      
      const response = await fetch(`http://192.168.1.15:5000/api/logs?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: LogsResponse = await response.json();

      // Debug: Log traceback information for failed entries
      data.logs.forEach((log, index) => {
        if (log.status === 'FAIL') {
          console.log(`Log ${index} - Status: ${log.status}, Message: ${log.message}, Traceback: "${log.traceback}"`);
        }
      });

      setLogs(data.logs);
    } catch (err) {
      console.error('Error fetching logs:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleApplyFilters = () => {
    fetchLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      startDate: '',
      endDate: '',
      status: '',
      limit: 1000
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
        return 'success';
      case 'FAIL':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatMessage = (message: string) => {
    // Truncate very long messages
    if (message.length > 200) {
      return message.substring(0, 200) + '...';
    }
    return message;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : 2,
          maxHeight: '90vh',
          height: fullScreen ? '100vh' : '80vh'
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1,
          background: (theme) =>
            theme.palette.mode === 'light'
              ? `linear-gradient(135deg, ${theme.palette.error.main} 0%, ${theme.palette.error.dark} 100%)`
              : `linear-gradient(135deg, ${theme.palette.error.light} 0%, ${theme.palette.error.main} 100%)`,
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BugReport />
          <Typography variant="h6" component="div">
            Application Logs
          </Typography>
        </Box>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Filters Section */}
        <Paper elevation={1} sx={{ p: 2, m: 2, mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <FilterList color="primary" />
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              Filters
            </Typography>
          </Box>
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Start Date"
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="End Date"
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="SUCCESS">Success</MenuItem>
                  <MenuItem value="FAIL">Fail</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                label="Limit"
                type="number"
                value={filters.limit}
                onChange={(e) => handleFilterChange('limit', parseInt(e.target.value) || 1000)}
                inputProps={{ min: 1, max: 10000 }}
                fullWidth
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleApplyFilters}
                  size="small"
                  startIcon={<Refresh />}
                  disabled={loading}
                >
                  Apply
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleClearFilters}
                  size="small"
                  disabled={loading}
                >
                  Clear
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Logs Content */}
        <Box sx={{ flex: 1, overflow: 'hidden', mx: 2, mb: 2 }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>Loading logs...</Typography>
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          ) : (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Showing {logs.length} log entries
                </Typography>
                <Button
                  variant="text"
                  size="small"
                  startIcon={<Refresh />}
                  onClick={fetchLogs}
                  disabled={loading}
                >
                  Refresh
                </Button>
              </Box>
              
              <Paper 
                elevation={1} 
                sx={{ 
                  height: '100%', 
                  overflow: 'auto',
                  border: 1,
                  borderColor: 'divider'
                }}
              >
                <List dense sx={{ p: 0 }}>
                  {logs.map((log, index) => (
                    <ListItem
                      key={index}
                      sx={{
                        borderBottom: index < logs.length - 1 ? 1 : 0,
                        borderColor: 'divider',
                        py: 1,
                        px: 2,
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        }
                      }}
                    >
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Chip
                              label={log.status}
                              size="small"
                              color={getStatusColor(log.status) as any}
                              sx={{ minWidth: 60, fontSize: '0.75rem' }}
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                              {log.date} {log.time}
                            </Typography>
                            <Chip
                              label={log.user_type}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: 20 }}
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                              {log.username}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                fontSize: '0.875rem',
                                wordBreak: 'break-word',
                                whiteSpace: 'pre-wrap'
                              }}
                            >
                              {formatMessage(log.message)}
                            </Typography>

                            {log.traceback && log.traceback.trim() && log.traceback.trim() !== "Traceback (most recent call last):" && (
                              <Paper
                                variant="outlined"
                                sx={{
                                  mt: 1,
                                  p: 1,
                                  bgcolor: (theme) => theme.palette.mode === 'dark' ? 'rgba(255, 0, 0, 0.05)' : 'rgba(255, 0, 0, 0.03)',
                                  borderColor: 'error.light'
                                }}
                              >
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: 'error.main',
                                    fontWeight: 600,
                                    display: 'block',
                                    mb: 0.5
                                  }}
                                >
                                  Traceback:
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    fontFamily: 'monospace',
                                    fontSize: '0.75rem',
                                    color: 'error.main',
                                    wordBreak: 'break-word',
                                    whiteSpace: 'pre-wrap',
                                    maxHeight: '200px',
                                    overflow: 'auto'
                                  }}
                                >
                                  {log.traceback}
                                </Typography>
                              </Paper>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                  {logs.length === 0 && (
                    <ListItem>
                      <ListItemText
                        primary={
                          <Typography variant="body2" color="text.secondary" textAlign="center">
                            No logs found matching the current filters
                          </Typography>
                        }
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, pt: 1 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LogsModal;
