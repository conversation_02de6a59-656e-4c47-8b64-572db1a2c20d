{"CreditCardSummary": {"format": {"type": "json_schema", "json_schema": {"name": "CreditCardSummary", "description": "Schema for extracting transaction details from a credit card statement.", "schema": {"type": "object", "properties": {"Account Number": {"type": "string", "description": "The account number as displayed on the statement, including any masking characters."}, "Previous Balance": {"type": "number", "description": "The previous balance from the last statement, formatted as displayed (e.g., '$701,781.66')."}, "Payment/Credits": {"type": "number", "description": "The Payment, Credits from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Purchases": {"type": "number", "description": "The Purchases from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Cash Advances": {"type": "number", "description": "The Cash Advances from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Balance Transfers": {"type": "number", "description": "The Balance Transfers from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Fees Charged": {"type": "number", "description": "The Fees Charged from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Interest Charged": {"type": "number", "description": "The Interest Charged from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "New Balance": {"type": "number", "description": "The New Balance from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Opening/Closing Date": {"type": "string", "description": "The Opening/Closing Date from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Revolving Credit Amount ": {"type": "number", "description": "The Revolving Credit Amount  from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Available Credit": {"type": "number", "description": "The Available Credit from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Cash Access Line": {"type": "number", "description": "The Cash Access Line from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Available for Cash": {"type": "number", "description": "The Available for Cash from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Past Due Amount": {"type": "number", "description": "The Past Due Amount from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Balance over the Credit Access Line": {"type": "number", "description": "The Balance over the Credit Access Line from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Payment Due Date": {"type": "number", "description": "The Payment Due Date from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Minimum Payment Due": {"type": "number", "description": "The Minimum Payment Due from the ACCOUNT SUMMARY, formatted as displayed (e.g., '$701,781.66')."}, "Transactions": {"type": "array", "description": "List of all check transactions extracted from the document.", "items": {"type": "object", "properties": {"Date": {"type": "string", "description": "The date of the transaction in MM/DD/YYYY format."}, "Description": {"type": "string", "description": "The description of the transaction."}, "Amount": {"type": "number", "description": "The exact check amount without any modifications or rounding take negative sign into account."}}, "required": ["Date", "Description", "Amount"], "additionalProperties": false}}}, "required": ["CreditCardSummary"], "additionalProperties": false}, "strict": true}}}}