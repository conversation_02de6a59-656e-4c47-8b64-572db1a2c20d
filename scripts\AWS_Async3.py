import asyncio
import aioboto3
import aiofiles
import json
import sys
import logging
from botocore.exceptions import ClientError
import uuid
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)  # Logs to stdout
    ]
)

async def upload_file_to_s3(s3_client, file_path, bucket_name, object_name=None):
    if object_name is None:
        object_name = os.path.basename(file_path)
    
    try:
        async with aiofiles.open(file_path, 'rb') as f:
            data = await f.read()
        await s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
        logging.info(f"Uploaded {file_path} to s3://{bucket_name}/{object_name}")
        return object_name
    except ClientError as e:
        logging.error(f"Failed to upload file to S3: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Unexpected error uploading file to S3: {e}")
        sys.exit(1)

async def start_document_text_detection(textract_client, bucket_name, object_name, job_tag="job", notification_channel=None):
    try:
        params = {
            'DocumentLocation': {
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': object_name
                }
            },
            'JobTag': job_tag
        }
        if notification_channel:
            params['NotificationChannel'] = notification_channel

        response = await textract_client.start_document_text_detection(**params)
        job_id = response['JobId']
        logging.info(f"Started text detection job with Job ID: {job_id}")
        return job_id
    except ClientError as e:
        logging.error(f"Error starting text detection: {e}")
        sys.exit(1)

async def get_document_text_detection(textract_client, job_id):
    try:
        while True:
            response = await textract_client.get_document_text_detection(JobId=job_id)
            status = response['JobStatus']
            logging.info(f"Job status: {status}")

            if status in ['SUCCEEDED', 'FAILED']:
                if status == 'SUCCEEDED':
                    logging.info("Text detection succeeded.")
                    blocks = response.get('Blocks', [])
                    next_token = response.get('NextToken', None)
                    while next_token:
                        logging.info("Retrieving next page of results...")
                        response = await textract_client.get_document_text_detection(JobId=job_id, NextToken=next_token)
                        blocks.extend(response.get('Blocks', []))
                        next_token = response.get('NextToken', None)
                    return blocks
                else:
                    logging.error("Text detection failed.")
                    sys.exit(1)
            await asyncio.sleep(5)
    except ClientError as e:
        logging.error(f"Error getting text detection results: {e}")
        sys.exit(1)

def get_text_in_order(blocks):
    lines = []
    for block in blocks:
        if block['BlockType'] == 'LINE':
            text = block.get('Text', '')
            page_num = block.get('Page', 1)
            top = block['Geometry']['BoundingBox']['Top']
            left = block['Geometry']['BoundingBox']['Left']
            lines.append((page_num, top, left, text))
    lines.sort(key=lambda x: (x[0], x[1], x[2]))
    ordered_text = [line[3] for line in lines]
    return ordered_text

async def process_document(local_pdf_path, bucket_name, object_name=None, job_tag="ExtractTextJob", notification_channel=None, 
                           base_filename=None):
    session = aioboto3.Session()

    if base_filename is None:
        base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    file_dir = os.path.dirname(os.path.abspath(local_pdf_path))
    output_folder = os.path.join(file_dir, base_filename)

    try:
        os.makedirs(output_folder, exist_ok=True)
        logging.info(f"Output folder created at: {output_folder}")
    except Exception as e:
        logging.error(f"Error creating output folder: {e}")
        sys.exit(1)

    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        if not object_name:
            object_name = f"textract-input/{uuid.uuid4()}-{os.path.basename(local_pdf_path)}"
        s3_key = await upload_file_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        logging.info("Starting text detection with Textract...")
        job_id = await start_document_text_detection(textract_client, bucket_name, s3_key, job_tag, notification_channel)

        logging.info("Polling for text detection results...")
        blocks = await get_document_text_detection(textract_client, job_id)

        logging.info("Extracting text in reading order...")
        ordered_text = get_text_in_order(blocks)
        text_json_filename = os.path.join(output_folder, f"{base_filename}_Text.json")
        try:
            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(ordered_text, ensure_ascii=False, indent=4))
            logging.info(f"Extracted text saved to {text_json_filename}.")
        except Exception as e:
            logging.error(f"Error writing to JSON file: {e}")
            sys.exit(1)

        logging.info("Creating combined text file...")
        combined_txt_filename = os.path.join(output_folder, f"{base_filename}_strUserPrompt.txt")
        try:
            async with aiofiles.open(combined_txt_filename, 'w', encoding='utf-8') as combined_file:
                for line in ordered_text:
                    await combined_file.write(line + "\n")
            logging.info(f"Combined text file created: {combined_txt_filename}.")
        except Exception as e:
            logging.error(f"Error writing to combined text file: {e}")
            sys.exit(1)

        logging.info("Saving AWS Textract blocks to JSON file...")
        blocks_json_filename = os.path.join(output_folder, f"{base_filename}_AWS_Object.json")
        try:
            async with aiofiles.open(blocks_json_filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(blocks, ensure_ascii=False, indent=4))
            logging.info(f"AWS Textract blocks saved to {blocks_json_filename}.")
        except Exception as e:
            logging.error(f"Error writing blocks JSON file: {e}")
            sys.exit(1)

        logging.info("AWS text extraction process completed successfully.")
        return blocks

async def extractByAwsTextract(local_pdf_path):
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None
    job_tag = "ExtractTextJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    dictExtractedData = await process_document(local_pdf_path, bucket_name, object_name, job_tag, notification_channel, base_filename)

if __name__ == "__main__":
    asyncio.run(extractByAwsTextract(r"H:\Customers\Parag Traders\mainProjectParagTraders\data\inputData\99_identifyVendor\2412011732.PDF"))
