# scripts/callApi.py

import openai
import json
import os
import logging
import traceback
import shutil
import asyncio
from openai import OpenAI
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from utils import MloadConfig, MloadSystemPrompt, MloadResponseFormat, MsetupLogging
from ensure import ensure_annotations
from typing import Dict, Any, Union
from AWS_Async import extractByAwsTextract


# NOTE
# This file is for Parag Traders vendor only
# Assumes PDF to text conversion file is inside the folder of the with same name file + "_Combined.txt" (abc.pdf is inside abc folder with abc_Combined.txt name)

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"
TEXT_EXTRACTEDCSV_SUFFIX = "_ExtractedText"


class CExtractByOpenai:
    def __init__(self, strFilePath, strVendorName="1_concor") -> None:

        self.m_config: Dict[str, Any] = MloadConfig()

        self.client = OpenAI()
        
        self.m_strFilePath = strFilePath
        self.m_strFolderPath = os.path.dirname(self.m_strFilePath)
        self.m_strFileName = os.path.basename(strFilePath)
        self.m_strFileNameWithoutExtension = os.path.splitext(self.m_strFileName)[0]
        self.m_strVendorName = strVendorName

        self.m_strSystemPromptOfAllVendors = self.m_config.get("systemPromptFilePath")
        self.m_strResponseFormatFilePath = self.m_config.get("responseFormatFilePath")

        self.m_apiResponsePath = self.m_config.get("apiResponsesPath")

    
    def MloadConfigurations(self):
        
        strTextFilePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension, self.m_strFileNameWithoutExtension+TEXT_EXTRACTEDCSV_SUFFIX+".txt")
        print("the new file path is  : " ,strTextFilePath)
        # Extract text with AWS Textraxt if not present
        if not os.path.exists(strTextFilePath):
            asyncio.run(extractByAwsTextract(self.m_strFilePath))

        # Load the user prompt
        with open(strTextFilePath, 'r') as file:
            self.m_strUserContentStructured = file.read()

        self.m_strSystemPrompt = self.m_strSystemPromptOfAllVendors

        # Load the response format
        with open(self.m_strResponseFormatFilePath, 'r') as file:
            self.m_strResponseFormatOfAllVendors = json.load(file)

        self.m_strResponseFormat = self.m_strResponseFormatOfAllVendors[self.m_strVendorName]
    
    def McallOpenAiApi(self):

        # try:
        print("------------- Started extraction by OpenAI ------------------")
        self.objResponse = self.client.responses.create(
                model="o3-mini-2025-01-31",   # o3-mini-2025-01-31  # gpt-4o-2024-11-20
                input=[
                    {"role": "system", "content": self.m_strSystemPrompt},
                    {"role": "user", "content": str(self.m_strUserContentStructured)}
                ],
                text=self.m_strResponseFormat,
                reasoning={"effort": "high"}
            )
        
        print("------------- OpenAI Extraction completed ------------------")
        return self.objResponse
        
        
        # except Exception as e:
        #     print(f"Exeption occured : {e}")
        # return None
        
    def MSaveResponse(self):
        
        strResponseFolderPath = os.path.join(self.m_apiResponsePath, self.m_strVendorName)
        os.makedirs(strResponseFolderPath, exist_ok=True)
        strResponseFileName = self.m_strFileNameWithoutExtension + GPT_RESPONSE_SUFFIX + ".json"
        strResponseFilePath = os.path.join(strResponseFolderPath, strResponseFileName)

        with open(strResponseFilePath, "w") as json_file:
            json.dump(self.objResponse.model_dump(), json_file, indent=4)

    def MMoveFile(self):
        
        # Moving pdf file
        strSourcePath = self.m_strFilePath
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        os.makedirs(strDestinationPath, exist_ok=True)
        shutil.move(strSourcePath, strDestinationPath)

        # moving folder of pdf file which has text and tables data
        strSourcePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension)
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        shutil.move(strSourcePath, strDestinationPath)



class CProcessDocument:

    def __init__(self, strVendorName="1_concor") -> None:
        self.m_dictConfig = MloadConfig()      # loads config in dict
        self.strVendorName = strVendorName


    def MprocessDocument(self, strFilePath) -> None:

        print(f"Processing {strFilePath}")
        objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName=self.strVendorName)
        strFileName = os.path.basename(strFilePath)
        strFileNameWithoutExtension = os.path.splitext(strFileName)[0]
        objCExtractByOpenai.MloadConfigurations()
        objCExtractByOpenai.McallOpenAiApi()
        objCExtractByOpenai.MSaveResponse()
        objCExtractByOpenai.MMoveFile()





    def MprocessAllDocuments(self, iFilesToProcess=1) -> None:

        strFolderPathToProcess = os.path.join(self.m_dictConfig.get("inputDataPath"), self.strVendorName)
        intTotalFilesProcessed = 0

        listFiles = os.listdir(strFolderPathToProcess)
        with ThreadPoolExecutor() as executor:
            futures = []
            for file in listFiles:
                if file.lower().endswith(".pdf"):
                    strFilePath = os.path.join(strFolderPathToProcess, file)
                    futures.append(executor.submit(self.MprocessDocument, strFilePath))
            for future in as_completed(futures):
                try:
                    future.result()
                    intTotalFilesProcessed += 1
                    if intTotalFilesProcessed >= iFilesToProcess:
                        break
                except Exception as e:
                    traceback.print_exc() 
                    logging.error(e)


if __name__ == "__main__":

    # strFilePath = r"H:\DEVELOPER_PUBLIC\interns\Satyam Tank\indianInvoicePosting\data\inputData\1_simpolo\2411007949"
    # strVendorName = "1_simpolo"
    # objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName)
    # objCExtractByOpenai.MloadConfigurations()
    # print("the new file")

    objCProcessDocument = CProcessDocument(strVendorName="creditInfo")                
    objCProcessDocument.MprocessAllDocuments(iFilesToProcess=10)     # processes all file
    
    # creditInfo  -  resource/CreditSystemPrompt_V1.txt
    # debitInfo  -  resource/DebitSystemPrompt_V1.txt
    # checkNumberInorder  -  resource/checkNumberInorderSysPrompt.txt
    # dailyEndingBalance  -  resource/dailyEndingBalanceSysPrompt.txt