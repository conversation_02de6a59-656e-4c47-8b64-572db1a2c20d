import React from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  Button,
  Divider,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import {
  ExpandMore,
  Upload,
  Download,
  History,
  Refresh,
  CheckCircle,
  Warning,
  Info,
  AccountBalance,
  Description,
  TableChart,
  CloudUpload,
  Schedule,
  Security,
  Home,
  Brightness4
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Header from '../components/Header';

const HelpPage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <Upload color="primary" />,
      title: "File Upload",
      description: "Upload US Bank statement PDF files with drag-and-drop or file browser"
    },
    {
      icon: <TableChart color="primary" />,
      title: "Data Extraction",
      description: "Automatically extract transaction data using AWS Textract and OpenAI"
    },
    {
      icon: <Download color="primary" />,
      title: "Excel Export",
      description: "Generate and download Excel files formatted for General Ledger import"
    },
    {
      icon: <History color="primary" />,
      title: "Processing History",
      description: "View and manage all processed files with detailed information"
    },
    {
      icon: <Refresh color="primary" />,
      title: "Real-time Updates",
      description: "Monitor processing status with automatic updates and notifications"
    },
    {
      icon: <Security color="primary" />,
      title: "Secure Processing",
      description: "Enterprise-grade security with AWS cloud infrastructure"
    }
  ];

  const supportedBanks = [
    "Belgrade State Bank", "ANBTX", "Unico"
  ];

  const supportedCreditCards = [
    "Chase"
  ];

  const fileFormats = [
    { format: "PDF", supported: true, description: "Portable Document Format - Primary supported format" }
  ];

  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Header title="Help & Documentation" />

      <Container maxWidth="lg" sx={{ py: 4, flex: 1 }}>
        {/* Back to Home Button */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<Home />}
            onClick={() => navigate('/')}
            sx={{ mb: 2 }}
          >
            Back to Home
          </Button>
        </Box>

        {/* Page Title */}
        <Typography variant="h3" component="h1" gutterBottom sx={{ mb: 4, fontWeight: 600 }}>
          US Bank Statements GL Processor
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
          Complete guide to using the bank statement processing application
        </Typography>

        {/* Quick Overview */}
        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom color="primary">
            <Info sx={{ mr: 1, verticalAlign: 'middle' }} />
            Quick Overview
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            This application processes US Bank statement PDFs and converts them into structured Excel files
            suitable for General Ledger import. It uses advanced AI technology (AWS Textract and OpenAI)
            to accurately extract transaction data from PDF documents.
          </Typography>
          <Alert severity="info" sx={{ mt: 2 }}>
            <strong>Processing Time:</strong> Typically takes 10-15 minutes per document depending on the number of pages and complexity.
          </Alert>
        </Paper>

        {/* Features Grid */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          Key Features
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr', lg: '1fr 1fr 1fr' }, gap: 3, mb: 4 }}>
          {features.map((feature, index) => (
            <Card elevation={2} sx={{ height: '100%' }} key={index}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {feature.icon}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {feature.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          ))}
        </Box>

        {/* Step-by-Step Guide */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          Step-by-Step Guide
        </Typography>

        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Step 1: Upload Your Bank Statement</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><CloudUpload color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Click the upload area or drag and drop your PDF file"
                  secondary="The system accepts PDF files up to 10MB in size"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Verify file information"
                  secondary="Check the file name, size, and page count before processing"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Upload color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Click 'Process File' to start"
                  secondary="The file will be uploaded and processing will begin automatically"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Step 2: Monitor Processing Status</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><Schedule color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Processing begins immediately"
                  secondary="Status changes to 'Under Processing' with a blue indicator"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Refresh color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Automatic status updates"
                  secondary="The system polls for completion every 10 seconds"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Warning color="warning" /></ListItemIcon>
                <ListItemText
                  primary="Avoid refreshing the page"
                  secondary="A warning will appear if you try to refresh during processing"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Step 3: View File Details</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1" sx={{ mb: 2 }}>
              When you select a file from the history panel, detailed information is displayed:
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon><Description color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Input File Name"
                  secondary="Original name of the uploaded PDF file"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Info color="primary" /></ListItemIcon>
                <ListItemText
                  primary="File Size"
                  secondary="Size of the PDF file in KB or MB"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Description color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Number of Pages"
                  secondary="Total page count in the PDF document"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Schedule color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Process Date/Time"
                  secondary="When the file was uploaded and processing started"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Status"
                  secondary="Available (green), Under Processing (blue), or Not Available (orange)"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Step 4: Download Results</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Wait for completion"
                  secondary="Status will change to 'Available' when processing is complete"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Download color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Download Excel file"
                  secondary="Click the 'Download Excel' button to get the processed file"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Description color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Download original PDF"
                  secondary="Click the 'Download PDF' button to get the original file"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Interface Guide */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3, mt: 4 }}>
          Interface Guide
        </Typography>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">History Panel (Left Side)</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1" sx={{ mb: 2 }}>
              The history panel shows all processed files and provides quick access to their details:
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon><History color="primary" /></ListItemIcon>
                <ListItemText
                  primary="File List"
                  secondary="Shows all uploaded files with names, dates, and file sizes"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Refresh color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Refresh Button"
                  secondary="Updates the history list with latest system data"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Download color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Download Icons"
                  secondary="Quick download buttons for each file (disabled during processing)"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Info color="primary" /></ListItemIcon>
                <ListItemText
                  primary="File Counter"
                  secondary="Shows total number of files in the system"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Main Content Area (Right Side)</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><Upload color="primary" /></ListItemIcon>
                <ListItemText
                  primary="File Upload Section"
                  secondary="Drag-and-drop area or click to browse for files"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Info color="primary" /></ListItemIcon>
                <ListItemText
                  primary="File Details Panel"
                  secondary="Shows detailed information when a file is selected"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Download color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Download Buttons"
                  secondary="Download Excel and PDF files when available"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Info color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Information Notes"
                  secondary="Important details about supported banks and processing times"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Header Navigation</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><AccountBalance color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Application Title"
                  secondary="US Bank Statements GL Processor - click to return to home"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Brightness4 color="primary" /></ListItemIcon>
                <ListItemText
                  primary="Theme Toggle"
                  secondary="Switch between light and dark themes"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Supported Banks and Formats */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3, mt: 4 }}>
          Supported Banks, Credit Cards & File Formats
        </Typography>

        <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' }, gap: 3, mb: 4 }}>
          <Card elevation={2}>
            <CardHeader
              title="Banks"
              avatar={<AccountBalance color="primary" />}
            />
            <CardContent>
              {supportedBanks.map((bank, index) => (
                <Chip
                  key={index}
                  label={bank}
                  color="primary"
                  variant="outlined"
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                More banks will be added in future updates
              </Typography>
            </CardContent>
          </Card>

          <Card elevation={2}>
            <CardHeader
              title="Credit Cards"
              avatar={<AccountBalance color="primary" />}
            />
            <CardContent>
              {supportedCreditCards.map((bank, index) => (
                <Chip
                  key={index}
                  label={bank}
                  color="primary"
                  variant="outlined"
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                More credit cards will be added in future updates
              </Typography>
            </CardContent>
          </Card>

          <Card elevation={2}>
            <CardHeader
              title="File Formats"
              avatar={<Description color="primary" />}
            />
            <CardContent>
              {fileFormats.map((format, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Chip
                    label={format.format}
                    color={format.supported ? "success" : "default"}
                    size="small"
                    sx={{ mr: 2, minWidth: 60 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    {format.description}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Box>

        {/* Status Indicators */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          Status Indicators
        </Typography>

        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' }, gap: 3 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Chip label="Available" color="success" sx={{ mb: 2, fontWeight: 'bold' }} />
              <Typography variant="body2">
                File has been processed successfully and Excel file is ready for download
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Chip label="Under Processing" color="info" sx={{ mb: 2, fontWeight: 'bold' }} />
              <Typography variant="body2">
                File is currently being processed. Please wait for completion
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Chip label="Not Available" color="warning" sx={{ mb: 2, fontWeight: 'bold' }} />
              <Typography variant="body2">
                Processing failed or Excel file is not available
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Troubleshooting */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          Troubleshooting
        </Typography>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Common Issues</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><Warning color="warning" /></ListItemIcon>
                <ListItemText
                  primary="File upload fails"
                  secondary="Check file size (max 50MB) and ensure it's a valid PDF format"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Warning color="warning" /></ListItemIcon>
                <ListItemText
                  primary="Processing takes too long"
                  secondary="Large files may take 15+ minutes. Check your internet connection"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Warning color="warning" /></ListItemIcon>
                <ListItemText
                  primary="Download button disabled"
                  secondary="File may still be processing or processing failed"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><Warning color="warning" /></ListItemIcon>
                <ListItemText
                  primary="History not updating"
                  secondary="Click the refresh button in the history panel"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="h6">Best Practices</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Use clear, high-quality PDF files"
                  secondary="Better quality documents produce more accurate results"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Don't refresh during processing"
                  secondary="This may interrupt the processing and cause errors"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Process one file at a time"
                  secondary="Wait for current processing to complete before uploading another file"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
                <ListItemText
                  primary="Keep the browser tab open"
                  secondary="Closing the tab may interrupt status updates"
                />
              </ListItem>
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Technical Information */}
        <Typography variant="h5" gutterBottom sx={{ mb: 3, mt: 4 }}>
          Technical Information
        </Typography>

        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Processing Technology
          </Typography>
          <List>
            <ListItem>
              <ListItemText
                primary="AWS Textract"
                secondary="Advanced OCR technology for accurate text extraction from PDF documents"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="OpenAI GPT"
                secondary="AI-powered data structuring and transaction categorization"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary="Excel Generation"
                secondary="Automated creation of Excel files formatted for General Ledger import"
              />
            </ListItem>
          </List>
        </Paper>

        {/* Footer */}
        <Divider sx={{ my: 4 }} />
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <Typography variant="body2" color="text.secondary">
            © {new Date().getFullYear()} US Bank GL Processing Tool - Help & Documentation
          </Typography>
          <Button
            variant="contained"
            startIcon={<Home />}
            onClick={() => navigate('/')}
            sx={{ mt: 2 }}
          >
            Return to Application
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default HelpPage;