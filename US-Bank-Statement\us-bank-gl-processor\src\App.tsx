import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './pages/LandingPage';
import HelpPage from './pages/HelpPage';
import { ThemeProvider } from './context/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <Router>
        {/* Removed Suspense since we're not using lazy loading */}
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/help" element={<HelpPage />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
