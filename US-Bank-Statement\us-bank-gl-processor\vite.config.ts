import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Optimize build
  build: {
    // Enable minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    // Split chunks for better caching
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          xlsx: ['xlsx'],
        },
      },
    },
  },

  // Optimize dev server
  server: {
    hmr: {
      overlay: true,
    },
    // Optimize memory usage
    fs: {
      strict: false, // Less strict file system checking
    },
    watch: {
      usePolling: true, // Use polling instead of native file system events
      interval: 1000, // Check for changes every second
      binaryInterval: 3000, // Check binary files less frequently
    },
  },

  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom', '@mui/material', '@mui/icons-material', 'xlsx'],
  },
})
