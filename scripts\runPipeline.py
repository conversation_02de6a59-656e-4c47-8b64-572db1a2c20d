# scripts/runPipeline.py

import logging
from utils import MloadConfig, MsetupLogging
from scripts.extractData import CEx<PERSON><PERSON>y<PERSON>penai
from compareResults import CCompareResults
from ensure import ensure_annotations
from typing import Any, Dict


 
def Mmain() -> None:
    '''
    Purpose : Orchestrate the entire data processing pipeline by extracting data using OpenAI API and comparing results.

    Inputs  : None

    Output  : None

    Example : Mmain()
    '''
    # Load configuration
    dictConfig: Dict[str, Any] = MloadConfig()
    
    # Set up logging for the pipeline
    MsetupLogging('logs/pipeline.log')
    objLogger: logging.Logger = logging.getLogger('Pipeline')
    objLogger.setLevel(logging.INFO)
    
    objLogger.info("Pipeline started.")
    
    # Step 1: Extract structured data using OpenAI API
    objExtractor: CExtractByOpenai = CExtractByOpenai()
    objExtractor.MprocessAllDocuments()
    
    # Step 2: Compare API responses with true data
    objComparer: CCompareResults = CCompareResults()
    objComparer.Mrun()
    
    objLogger.info("Pipeline completed successfully.")

if __name__ == "__main__":
    Mmain()
