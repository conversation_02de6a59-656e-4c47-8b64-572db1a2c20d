#!/usr/bin/env python3
"""
Simplified API Server for testing the history endpoint
"""

import os
import json
from datetime import datetime
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

def get_pdf_page_count(pdf_path):
    """Simplified page count function - returns None for now"""
    return None

@app.route('/api/history', methods=['GET'])
def get_history():
    """
    API endpoint to get the system-wide processing history.
    Returns a list of all processed files with their details.
    """
    try:
        history = []
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")

        if not os.path.exists(data_dir):
            return jsonify({'history': []}), 200

        # List all timestamped directories in Data1
        for timestamp_dir in sorted(os.listdir(data_dir), reverse=True):
            timestamp_path = os.path.join(data_dir, timestamp_dir)

            # Skip if not a directory
            if not os.path.isdir(timestamp_path):
                continue

            # Check for input PDF
            input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
            if not os.path.exists(input_pdf_dir):
                continue

            # Get the PDF file name
            pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
            if not pdf_files:
                continue

            # Check if Excel file was generated
            excel_file_path = None
            output_dir = os.path.join(timestamp_path, "output")
            if os.path.exists(output_dir):
                excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
                if excel_files:
                    excel_file_path = os.path.join(output_dir, excel_files[0])

            # Get file size and page count
            pdf_path = os.path.join(input_pdf_dir, pdf_files[0])
            file_size = os.path.getsize(pdf_path)
            file_size_str = f"{file_size / 1024 / 1024:.2f} MB" if file_size > 1024 * 1024 else f"{file_size / 1024:.0f} KB"
            page_count = get_pdf_page_count(pdf_path)

            # Parse timestamp from directory name
            try:
                # Format: YYYYMMDD_HHMMSS_randomsuffix
                # Extract the datetime part (before the random suffix)
                timestamp_parts = timestamp_dir.split('_')
                if len(timestamp_parts) >= 2:
                    # Get the date and time parts
                    date_part = timestamp_parts[0]
                    time_part = timestamp_parts[1]

                    # Parse the date and time
                    year = int(date_part[0:4])
                    month = int(date_part[4:6])
                    day = int(date_part[6:8])
                    hour = int(time_part[0:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])

                    # Create ISO format timestamp
                    timestamp = f"{year:04d}-{month:02d}-{day:02d}T{hour:02d}:{minute:02d}:{second:02d}"
                else:
                    # If the format doesn't match, use the directory name as timestamp
                    timestamp = timestamp_dir
            except (ValueError, IndexError):
                # If parsing fails, use the directory name as timestamp
                timestamp = timestamp_dir

            # Try to read processing metadata
            processing_type = None
            processing_name = None
            metadata_path = os.path.join(timestamp_path, "processing_metadata.json")
            if os.path.exists(metadata_path):
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                        processing_type = metadata.get('processingType')
                        processing_name = metadata.get('processingName')
                except Exception as e:
                    print(f"Could not read metadata for {timestamp_dir}: {e}")

            # Add to history
            history.append({
                "id": timestamp_dir,
                "fileName": pdf_files[0],
                "uploadDate": timestamp,
                "fileSize": file_size_str,
                "pageCount": page_count,
                "excelPath": excel_file_path,
                "processingType": processing_type,
                "processingName": processing_name
            })

        return jsonify({'history': history}), 200

    except Exception as e:
        print(f"Error getting history: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/ui-listing', methods=['GET'])
def get_ui_listing():
    """
    API endpoint to get the UI listing configuration for Banks and Credit Cards.
    Returns the JSON configuration from config/frontendUIListing.json
    """
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'frontendUIListing.json')

        if not os.path.exists(config_path):
            print(f"UI listing config file not found: {config_path}")
            return jsonify({'error': 'Configuration file not found'}), 404

        with open(config_path, 'r', encoding='utf-8') as f:
            ui_listing = json.load(f)

        print("UI listing configuration retrieved successfully")
        return jsonify(ui_listing)

    except Exception as e:
        print(f"Error reading UI listing configuration: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Simple health check endpoint"""
    return jsonify({'status': 'ok'}), 200

if __name__ == '__main__':
    print("Starting test API server...")
    print("History endpoint available at: http://localhost:5000/api/history")
    print("UI listing endpoint available at: http://localhost:5000/api/ui-listing")
    app.run(host='0.0.0.0', port=5000, debug=True)
