{"frontend": {"url": "http://************:5173", "timeout": 5}, "backend": {"url": "http://************:5000", "api_endpoint": "/api/health", "timeout": 5}, "email": {"enabled": true, "smtp_server": "smtp.zoho.in", "smtp_port": 587, "username": "<EMAIL>", "password": "WSuNegdfaGA5", "sender": "<EMAIL>", "recipients": {"success": ["<EMAIL>"], "failure": ["<EMAIL>", "<EMAIL>"]}, "subject_prefix": "[US Bank Statement Processor - Service Health Check]"}}