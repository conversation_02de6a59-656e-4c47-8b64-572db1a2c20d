import json
import sys
import logging
from pathlib import Path
from typing import Any, Dict, List, Tuple
import pandas as pd
from datetime import datetime
import os

def setup_logging():
    """
    Configures the logging settings.
    Logs are printed to the console with the level INFO and above.
    """
    logging.basicConfig(
        level=logging.INFO,  # Change to DEBUG for more detailed logs
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_json(file_path: Path) -> Dict[str, Any]:
    """
    Loads a JSON file and returns its content as a dictionary.
    """
    try:
        logging.info(f"Loading JSON file: {file_path}")
        with file_path.open('r', encoding='utf-8') as f:
            data = json.load(f)
        logging.info(f"Successfully loaded JSON file: {file_path}")
        return data
    except FileNotFoundError:
        logging.error(f"File not found: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"Invalid JSON format in file {file_path}: {e}")
        return {}

def derive_json_paths(pdf_path: Path) -> Tuple[Path, Path]:
    """
    Given a PDF file path, derive the paths to JSON1 (API Response) and JSON2 (True Data).
    """
    logging.info(f"Deriving JSON paths from PDF path: {pdf_path}")
    
    # Ensure the input is a PDF file
    if pdf_path.suffix.lower() != '.pdf':
        logging.error("The input file must be a PDF.")
        return (None, None)
    
    # Extract the base filename without extension
    base_filename = pdf_path.stem  # e.g., 'deskew_2412009225'
    logging.debug(f"Base filename extracted: {base_filename}")
    
    # Define the root data directory
    # Input PDF path example:
    # H:\Customers\Parag Traders\mainProjectParagTraders\data\inputData\1_simpolo\processed\deskew_2412009225.pdf
    try:
        # Navigate up to 'data' directory (parents[3])
        # parents[0] = processed
        # parents[1] = 1_simpolo
        # parents[2] = inputData
        # parents[3] = data
        root_data_dir = pdf_path.parents[3]
        logging.info(f"Root data directory identified: {root_data_dir}")
    except IndexError:
        logging.error("The input PDF path does not have enough parent directories to locate 'data' directory.")
        return (None, None)
    
    # Derive JSON1 path (API Response)
    # Path: data\apiResponses\1_simpolo\deskew_2412009225_gptResponse.json
    api_responses_dir = root_data_dir / "apiResponses" / pdf_path.parent.parent.name  # '1_simpolo'
    json1_filename = f"{base_filename}_gptResponse.json"
    json1_path = api_responses_dir / json1_filename
    logging.info(f"Derived JSON1 (API Response) path: {json1_path}")
    
    # Derive JSON2 path (True Data)
    # Path: data\trueData\1_simpolo\deskew_2412009225_trueData.json
    true_data_dir = root_data_dir / "trueData" / pdf_path.parent.parent.name  # '1_simpolo'
    json2_filename = f"{base_filename}_trueData.json"
    json2_path = true_data_dir / json2_filename
    logging.info(f"Derived JSON2 (True Data) path: {json2_path}")
    
    return (json1_path, json2_path)

def extract_content_from_api_response(json1: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extracts the content at data["choices"][0]["message"]["content"] from the API Response JSON.
    Assumes that the content is a JSON string.
    """
    try:
        content_str = json1["choices"][0]["message"]["content"]
        logging.info("Extracted 'choices[0].message.content' from API Response.")
    except (KeyError, IndexError) as e:
        logging.error(f"Error extracting 'choices[0].message.content' from API Response: {e}")
        return {}
    
    try:
        content_json = json.loads(content_str)
        logging.info("Successfully parsed 'choices[0].message.content' as JSON.")
        return content_json
    except json.JSONDecodeError as e:
        logging.error(f"Error parsing 'choices[0].message.content' as JSON: {e}")
        return {}

def save_json_locally(json_data: Dict[str, Any], filename: str, script_dir: Path):
    """
    Saves the given JSON data to a file in the script's directory.
    """
    save_path = script_dir / filename
    try:
        with save_path.open('w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=4)
        logging.info(f"Saved JSON data to: {save_path}")
    except Exception as e:
        logging.error(f"Failed to save JSON data to {save_path}: {e}")

from typing import Any, List, Dict
import logging

def normalize_string(s: str) -> str:
    """
    Normalize a string by converting it to lowercase, stripping leading/trailing whitespace,
    and replacing multiple internal spaces with a single space.
    """
    return '0'.join(s.lower().strip().split())

def compare_jsons(json1: Any, json2: Any, path: str = "") -> List[Dict[str, Any]]:
    """
    Recursively compares two JSON objects and returns a list of differences.
    Each difference is represented as a dictionary with keys: 'Key Path', 'API Response', 'True Data'.
    Comparison is case-insensitive for both keys and string values, and it normalizes whitespace.
    """
    differences = []
    
    if isinstance(json1, dict) and isinstance(json2, dict):
        # Create mappings of lowercase keys to original keys for both JSON objects
        keys1_map = {k.lower(): k for k in json1.keys()}
        keys2_map = {k.lower(): k for k in json2.keys()}
        
        keys1_lower = set(keys1_map.keys())
        keys2_lower = set(keys2_map.keys())
        
        # Keys present in json1 but missing in json2
        for key_lower in keys1_lower - keys2_lower:
            key = keys1_map[key_lower]
            differences.append({
                "Key Path": f"{path}{key}",
                "API Response": json1[key],
                "True Data": "Key missing in TrueData file."
            })
            logging.debug(f"Difference found: Key '{path + key}' missing in TrueData file.")
        
        # Keys present in json2 but missing in json1
        for key_lower in keys2_lower - keys1_lower:
            key = keys2_map[key_lower]
            differences.append({
                "Key Path": f"{path}{key}",
                "API Response": "Key missing in API Response content.",
                "True Data": json2[key]
            })
            logging.debug(f"Difference found: Key '{path + key}' missing in API Response content.")
        
        # Keys present in both JSON objects (case-insensitive)
        for key_lower in keys1_lower & keys2_lower:
            key1 = keys1_map[key_lower]
            key2 = keys2_map[key_lower]
            new_path = f"{path}{key1}."
            differences.extend(compare_jsons(json1[key1], json2[key2], new_path))
    
    elif isinstance(json1, list) and isinstance(json2, list):
        min_length = min(len(json1), len(json2))
        for index in range(min_length):
            new_path = f"{path}[{index}]."
            differences.extend(compare_jsons(json1[index], json2[index], new_path))
        
        if len(json1) > len(json2):
            for index in range(min_length, len(json1)):
                differences.append({
                    "Key Path": f"{path}[{index}]",
                    "API Response": json1[index],
                    "True Data": "Item missing in TrueData file."
                })
                logging.debug(f"Difference found: Item at '{path}[{index}]' missing in TrueData file.")
        
        elif len(json2) > len(json1):
            for index in range(min_length, len(json2)):
                differences.append({
                    "Key Path": f"{path}[{index}]",
                    "API Response": "Item missing in API Response content.",
                    "True Data": json2[index]
                })
                logging.debug(f"Difference found: Item at '{path}[{index}]' missing in API Response content.")
    
    else:
        # Compare string values case-insensitively if both are strings
        if isinstance(json1, str) and isinstance(json2, str):
            norm1 = normalize_string(json1)
            norm2 = normalize_string(json2)
            if norm1 != norm2:
                differences.append({
                    "Key Path": path.rstrip('.'),
                    "API Response": json1,
                    "True Data": json2
                })
                logging.debug(
                    f"Difference found: Value mismatch at '{path.rstrip('.')}' - "
                    f"API Response: '{json1}' | True Data: '{json2}'"
                )
        else:
            if json1 != json2:
                differences.append({
                    "Key Path": path.rstrip('.'),
                    "API Response": json1,
                    "True Data": json2
                })
                logging.debug(
                    f"Difference found: Value mismatch at '{path.rstrip('.')}' - "
                    f"API Response: '{json1}' | True Data: '{json2}'"
                )
    
    return differences



def process_pdf(pdf_path: Path, script_dir: Path) -> List[Dict[str, Any]]:
    """
    Processes a single PDF file: derives JSON paths, loads JSONs, extracts content, compares, and collects differences.
    Also saves the extracted and trueData JSONs locally.
    Returns a list of differences.
    """
    differences = []
    json1_path, json2_path = derive_json_paths(pdf_path)
    
    # Check if paths are valid
    if not json1_path or not json2_path:
        logging.error(f"Skipping file due to path derivation issues: {pdf_path}")
        return differences
    
    # Check if JSON1 exists
    if not json1_path.exists():
        logging.error(f"JSON1 (API Response) file not found at: {json1_path}")
        return differences
    else:
        logging.info(f"JSON1 (API Response) file found: {json1_path}")
    
    # Check if JSON2 exists
    if not json2_path.exists():
        logging.error(f"JSON2 (True Data) file not found at: {json2_path}")
        return differences
    else:
        logging.info(f"JSON2 (True Data) file found: {json2_path}")
    
    # Load JSON1 (API Response)
    json1 = load_json(json1_path)
    if not json1:
        logging.error(f"Failed to load JSON1 for file: {pdf_path}")
        return differences
    
    # Extract and parse content from JSON1
    content_json = extract_content_from_api_response(json1)
    if not content_json:
        logging.error(f"Failed to extract content from JSON1 for file: {pdf_path}")
        return differences
    
    # Save extracted API Response content locally
    extracted_filename = f"{pdf_path.stem}_extracted_API_Response.json"
    # save_json_locally(content_json, extracted_filename, script_dir)
    
    # Load JSON2 (True Data)
    json2 = load_json(json2_path)
    if not json2:
        logging.error(f"Failed to load JSON2 for file: {pdf_path}")
        return differences
    
    # Save TrueData JSON locally
    true_data_filename = f"{pdf_path.stem}_trueData.json"
    # save_json_locally(json2, true_data_filename, script_dir)
    
    # Compare JSON files
    logging.info(f"Comparing JSON contents for file: {pdf_path.name}")
    differences = compare_jsons(content_json, json2)
    
    if not differences:
        logging.info(f"No differences found for file: {pdf_path.name}")
    else:
        logging.info(f"Differences found for file: {pdf_path.name}")
    
    return differences

def main(folder_path: str):
    """
    Main function to compare JSON files within a folder and generate an Excel report.
    """
    setup_logging()
    
    logging.info(f"Starting JSON comparison for folder: {folder_path}")
    
    folder = Path(folder_path).resolve()
    
    if not folder.exists() or not folder.is_dir():
        logging.error(f"The provided folder does not exist or is not a directory: {folder}")
        sys.exit(1)
    
    # Prepare to collect all differences
    all_differences = []
    
    # Get script directory to save extracted JSONs and Excel report
    script_dir = Path(__file__).parent.resolve()
    
    # Iterate over all PDF files in the folder and subfolders
    pdf_files = list(folder.rglob('*.pdf'))
    if not pdf_files:
        logging.warning(f"No PDF files found in the folder: {folder}")
        sys.exit(0)
    
    logging.info(f"Found {len(pdf_files)} PDF file(s) to process.")
    
    for pdf in pdf_files:
        logging.info(f"Processing file: {pdf}")
        differences = process_pdf(pdf, script_dir)
        for diff in differences:
            diff_entry = {
                "File Name": pdf.name,
                "Key Path": diff.get("Key Path", ""),
                "API Response": diff.get("API Response", ""),
                "True Data": diff.get("True Data", "")
            }
            all_differences.append(diff_entry)
    
    if not all_differences:
        logging.info("No differences found across all files.")
    else:
        # Create a DataFrame from the differences
        df = pd.DataFrame(all_differences)
        

        # Define the Excel file path
        script_dir = Path(script_dir)
        strDateTime = datetime.now().strftime("%Y-%m-%d--%H-%M-%S")
        excel_path = script_dir / f"iFileNum_{strDateTime}_comparison_results.xlsx"
        
        # Define the Excel file path
        # excel_path = script_dir / "comparison_results.xlsx"
        
        # Save the DataFrame to an Excel file
        try:
            df.to_excel(excel_path, index=False)
            logging.info(f"Comparison results saved to Excel file: {excel_path}")
        except Exception as e:
            logging.error(f"Failed to save comparison results to Excel: {e}")
            sys.exit(1)
    
    logging.info("JSON comparison process completed.")


if __name__ == "__main__":
 
    main(r"Z:\DEVELOPER_PUBLIC\interns\Satyam Tank\indianInvoicePosting\Data1\inputData\General_Schema\processed")
