import pandas as pd
import json

class BalanceChecker:
    def __init__(self, credit_path, debit_path, check_path, end_path):
        self.credit_path = credit_path
        self.debit_path = debit_path
        self.check_path = check_path
        self.end_path = end_path

    def load_data(self):
        self.df_credit = pd.json_normalize(pd.read_json(self.credit_path)["CreditsInfo"])
        self.df_debit = pd.json_normalize(pd.read_json(self.debit_path)["DebitInfo"])
        self.df_check = pd.json_normalize(pd.read_json(self.check_path)["CheckNumberinOrder"])
        self.df_end = pd.json_normalize(pd.read_json(self.end_path)["dailyEndingBalance"])

    def preprocess_data(self):
        # Example: extracting year from statement_start_date if needed
        with open(self.credit_path, "r") as f:
            json_data = json.load(f)
        statement_start_date = json_data.get("StatementStartDate", "")
        self.year = pd.to_datetime(statement_start_date).year if statement_start_date else None

    def calculate_balance_match(self):
        # Dummy balance match logic — replace with actual logic from your notebook
        self.df_end["BalanceMatch"] = self.df_end["EndBalance"] == self.df_end["EndBalance"].shift(1)

    def get_accuracy(self):
        if "BalanceMatch" not in self.df_end.columns:
            raise ValueError("BalanceMatch column not found. Run calculate_balance_match first.")
        total = len(self.df_end)
        correct = self.df_end["BalanceMatch"].sum()
        accuracy = correct / total if total > 0 else 0
        return accuracy

    def run(self):
        self.load_data()
        self.preprocess_data()
        self.calculate_balance_match()
        return self.get_accuracy()
