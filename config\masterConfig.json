{"Banks": {"Belgrade State Bank": {"bankName": "Belgrade State Bank", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/ResponseFormat_grok_V2.json", "systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9, "pdfSeparotor": {"previousBalance": "Previous Balance", "depositsAndOtherCredits": "Deposits and Other Credits", "checksAndOtherDebits": "Checks and Other Debits", "checksInNumberOrder": "CHECKS IN NUMBER ORDER", "dailyEndingBalance": "Daily Ending Balance", "checkAmountDate": "Check Amount Date"}, "creditInfo": {"systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt"}, "debitInfo": {"systemPromptFilePath": "resource/DebitSystemPrompt_V1.txt"}, "checkNumberInorder": {"systemPromptFilePath": "resource/checkNumberInorderSysPrompt.txt"}, "dailyEndingBalance": {"systemPromptFilePath": "resource/dailyEndingBalanceSysPrompt.txt"}, "OpeningPageInfo": {"systemPromptFilePath": "resource/OpeningPageSystemPromptV1.txt"}, "checkNumberFromImagePage": {"systemPromptFilePath": "resource/checkNumberFromChecksImagePage.txt"}}, "ANBTX": {"bankName": "ANBTX", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/ResponseFormat_grok_V2.json", "systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9, "pdfSeparotor": {"previousBalance": "ACCOUNT SUMMARY", "depositsAndOtherCredits": "DEPOSIT AND CREDITS", "checksAndOtherDebits": "WITHDRAWALS AND DEBITS", "checksInNumberOrder": "CHECKS PAID", "dailyEndingBalance": "DAILY BALANCE SUMMARY", "checkAmountDate": "XXXXXX7008"}, "creditInfo": {"systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt"}, "debitInfo": {"systemPromptFilePath": "resource/DebitSystemPrompt_V1.txt"}, "checkNumberInorder": {"systemPromptFilePath": "resource/checkNumberInorderSysPrompt.txt"}, "dailyEndingBalance": {"systemPromptFilePath": "resource/dailyEndingBalanceSysPrompt.txt"}, "OpeningPageInfo": {"systemPromptFilePath": "resource/OpeningPageSystemPromptV1.txt"}, "checkNumberFromImagePage": {"systemPromptFilePath": "resource/checkNumberFromChecksImagePage.txt"}}, "Unico Bank Digital": {"bankName": "Unico Bank Digital", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/ResponseFormat_grok_V2.json", "systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9, "pdfSeparotor": {"previousBalance": "Beginning Balance", "depositsAndOtherCredits": "Deposits/Other Credits", "checksAndOtherDebits": "Other Debits", "checksInNumberOrder": "Checks listed in numerical order;", "dailyEndingBalance": "Daily Ending Balance", "checkAmountDate": "POTOSI HEATING _COOLING INC 32"}, "creditInfo": {"systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt"}, "debitInfo": {"systemPromptFilePath": "resource/unico/DebitSystemPrompt_V1.txt"}, "checkNumberInorder": {"systemPromptFilePath": "resource/checkNumberInorderSysPrompt.txt"}, "dailyEndingBalance": {"systemPromptFilePath": "resource/dailyEndingBalanceSysPrompt.txt"}, "OpeningPageInfo": {"systemPromptFilePath": "resource/OpeningPageSystemPromptV1.txt"}, "checkNumberFromImagePage": {"systemPromptFilePath": "resource/checkNumberFromChecksImagePage.txt"}}, "Unico Bank Scanned": {"bankName": "Unico Bank Scanned", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/ResponseFormat_grok_V2.json", "systemPromptFilePath": "resource/CreditSystemPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9, "pdfSeparotor": {"previousBalance": "Account Summary", "depositsAndOtherCredits": "Deposits", "checksAndOtherDebits": "Electronic Debits", "checksInNumberOrder": "Checks Cleared", "dailyEndingBalance": "Daily Balances", "checkAmountDate": "#"}, "creditInfo": {"systemPromptFilePath": "resource/unicoScanned/CreditSystemPrompt_V1.txt"}, "debitInfo": {"systemPromptFilePath": "resource/unico/DebitSystemPrompt_V1.txt"}, "checkNumberInorder": {"systemPromptFilePath": "resource/checkNumberInorderSysPrompt.txt"}, "dailyEndingBalance": {"systemPromptFilePath": "resource/dailyEndingBalanceSysPrompt.txt"}, "OpeningPageInfo": {"systemPromptFilePath": "resource/OpeningPageSystemPromptV1.txt"}, "checkNumberFromImagePage": {"systemPromptFilePath": "resource/checkNumberFromChecksImagePage.txt"}}}, "Credit Cards": {"Chase": {"bankName": "Chase", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/chase/ResponseFormat_grok_V2_CC.json", "systemPromptFilePath": "resource/chase/CreditCardPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9}, "Bank of America": {"bankName": "Bank of America", "openaiApiKey": "***************************************************", "inputDataPath": "data/inputData", "trueDataPath": "data/trueData/", "apiResponsesPath": "data/apiResponses/", "processedDataPath": "data/processedData/", "responseFormatFilePath": "resource/BoFA/ResponseFormat_grok_V2_CC.json", "systemPromptFilePath": "resource/BoFA/CreditCardPrompt_V1.txt", "logFilePathExtract": "logs/extractByOpenai.log", "logFilePathCompare": "logs/compareResults.log", "maxApiWorkers": 10, "maxCompareWorkers": 5, "vendorWise": true, "similarityThreshold": 0.9}}}