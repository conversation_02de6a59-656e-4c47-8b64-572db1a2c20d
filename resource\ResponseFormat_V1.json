{"OpeningPageInfo": {"format": {"type": "json_schema", "name": "ExtractOpeningPageInfo", "description": "Schema for extracting summary information from the opening page of a US bank statement.", "schema": {"type": "object", "properties": {"Account Number": {"type": "string", "description": "The account number as displayed on the statement, including any masking characters."}, "Deposits/Credits": {"type": "object", "properties": {"Count": {"type": "integer", "description": "The total number of deposits and credits during the statement period."}, "Total Amount": {"type": "string", "description": "The total amount of all deposits and credits, formatted as displayed (e.g., '427,358.89')."}}, "required": ["Count", "Total Amount"], "additionalProperties": false}, "Checks/Debits": {"type": "object", "properties": {"Count": {"type": "integer", "description": "The total number of checks and debits during the statement period."}, "Total Amount": {"type": "string", "description": "The total amount of all checks and debits, formatted as displayed (e.g., '645,979.88')."}}, "required": ["Count", "Total Amount"], "additionalProperties": false}, "Service Charge": {"type": "string", "description": "The service charge amount for the statement period, formatted as displayed (e.g., '15.00')."}, "Interest Paid": {"type": "string", "description": "The interest paid amount for the statement period, formatted as displayed (e.g., '.00')."}, "Previous Balance": {"type": "string", "description": "The previous balance from the last statement, formatted as displayed (e.g., '701,781.66')."}, "Current Balance": {"type": "string", "description": "The current balance at the end of the statement period, formatted as displayed (e.g., '483,145.67')."}, "Statement Period": {"type": "object", "properties": {"Start Date": {"type": "string", "description": "The start date of the statement period, formatted as displayed (e.g., '1/01/24')."}, "End Date": {"type": "string", "description": "The end date of the statement period, formatted as displayed (e.g., '1/31/24')."}, "Days in Period": {"type": "integer", "description": "The total number of days in the statement period."}}, "required": ["Start Date", "End Date", "Days in Period"], "additionalProperties": false}}, "required": ["Account Number", "Deposits/Credits", "Checks/Debits", "Service Charge", "Interest Paid", "Previous Balance", "Current Balance", "Statement Period"], "additionalProperties": false}, "strict": true}}, "creditInfo": {"format": {"type": "json_schema", "name": "ExtractCreditsInfo", "description": "Schema for extracting deposit and credit transaction details from a US bank statement.", "schema": {"type": "object", "properties": {"CreditsInfo": {"type": "array", "description": "List of all deposit and credit transactions extracted from the document.", "items": {"type": "object", "properties": {"Date": {"type": "string", "description": "The date of the credit transaction in MM/DD format."}, "Description": {"type": "string", "description": "A brief description of the transaction, including the source or type of deposit."}, "Amount": {"type": "number", "description": "The exact credited amount without any modifications or rounding."}}, "required": ["Date", "Description", "Amount"], "additionalProperties": false}}, "Previous Balannce": {"type": "string", "description": "Extract the previous balance from the First Page"}, "StatementStartData": {"type": "string", "description": "Extract the Statement Start Data"}, "StatementEndDate": {"type": "string", "description": "Extract the Statement End Data"}}, "required": ["CreditsInfo", "Previous Balannce", "StatementStartData", "StatementEndDate"], "additionalProperties": false}, "strict": true}}, "debitInfo": {"format": {"type": "json_schema", "name": "ExtractDebitInfo", "description": "Schema for extracting debit transaction details from a US bank statement.", "schema": {"type": "object", "properties": {"DebitInfo": {"type": "array", "description": "List of all debit transactions extracted from the document.", "items": {"type": "object", "properties": {"Date": {"type": "string", "description": "The date of the debit transaction in MM/DD format."}, "Description": {"type": "string", "description": "A brief description of the transaction, including the source or type of debits."}, "Amount": {"type": "number", "description": "The exact debited amount without any modifications or rounding."}}, "required": ["Date", "Description", "Amount"], "additionalProperties": false}}}, "required": ["DebitInfo"], "additionalProperties": false}, "strict": true}}, "checkNumberInorder": {"format": {"type": "json_schema", "name": "CheckNumberinOrder", "description": "Schema for extracting check transaction details from a US bank statement.", "schema": {"type": "object", "properties": {"CheckNumberinOrder": {"type": "array", "description": "List of all check transactions extracted from the document.", "items": {"type": "object", "properties": {"Date": {"type": "string", "description": "The date of the check transaction in MM/DD format."}, "CheckNo": {"type": "number", "description": "The check number associated with the check transaction"}, "Amount": {"type": "number", "description": "The exact check amount without any modifications or rounding."}}, "required": ["Date", "CheckNo", "Amount"], "additionalProperties": false}}}, "required": ["CheckNumberinOrder"], "additionalProperties": false}, "strict": true}}, "dailyEndingBalance": {"format": {"type": "json_schema", "name": "dailyEndingBalance", "description": "Schema for extracting daily ending balance details from a US bank statement.", "schema": {"type": "object", "properties": {"dailyEndingBalance": {"type": "array", "description": "List of all daily ending balances extracted from the document.", "items": {"type": "object", "properties": {"Date": {"type": "string", "description": "The date of the recorded daily ending balance in MM/DD format."}, "Balance": {"type": "number", "description": "The exact balance at the end of the specified date without any modifications or rounding."}}, "required": ["Date", "Balance"], "additionalProperties": false}}}, "required": ["dailyEndingBalance"], "additionalProperties": false}, "strict": true}}}