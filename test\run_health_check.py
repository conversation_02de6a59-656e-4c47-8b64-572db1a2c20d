#!/usr/bin/env python3
"""
Run Service Health Check

This script runs the service health check tests locally.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import the test module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the test module
from test.test_service_health import ServiceHealthTest

if __name__ == '__main__':
    # Create a test suite
    suite = unittest.TestSuite()
    
    # Add the tests
    suite.addTest(ServiceHealthTest('test_frontend_is_running'))
    suite.addTest(ServiceHealthTest('test_backend_is_running'))
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Exit with appropriate code
    sys.exit(not result.wasSuccessful())
