#!/usr/bin/env python3
"""
API Server for US Bank Statement Processor

This script creates a Flask API server that connects the frontend to the bank statement processing functionality.
It accepts PDF file uploads, processes them using the existing functionality, and returns the Excel file for download.
"""

import os
import sys
import asyncio
import logging
import time
import traceback
import uuid
import json
from pathlib import Path
from typing import Optional
from flask import Flask, request, jsonify, send_file, after_this_request
from flask_cors import CORS
import shutil
from PyPDF2 import PdfReader

# Import the process_bank_statement function from bankStatement.py
from src.bankStatement import process_bank_statement
from src.creditCards import process_credit_cards

# Import the Excel report generation function
try:
    from src.excel import process_json_and_generate_report
    from src.excelCreditCard import creditCardExcel
except ImportError:
    process_json_and_generate_report = None
    print("Warning: Excel module not found. Excel report generation will be disabled.")

# Configure logging
def setup_logging():
    """Set up logging to both console and file."""
    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Use a single log file named "log"
    log_file = os.path.join(logs_dir, "log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers to prevent duplicate logs
    if logger.handlers:
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Create file handler with append mode
    file_handler = logging.FileHandler(log_file, mode='a')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Set up logging
logger = setup_logging()
logger.info(f"API Server logging initialized. Log file: {os.path.join('logs', os.path.basename(logger.handlers[0].baseFilename))}")

def log_structured_event(event_type, message, user_type="dev", username="Accuvelocity", traceback_info=None):
    """
    Log structured events for success/fail operations.

    Args:
        event_type: "SUCCESS" or "FAIL"
        message: The log message
        user_type: "dev" or "user" (default: "dev")
        username: Username (default: "Accuvelocity")
        traceback_info: Traceback information for failures (optional)
    """
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')

    if traceback_info:
        # For failures with traceback, combine message and traceback
        # Replace newlines with a special marker to preserve multiline tracebacks
        traceback_escaped = traceback_info.replace('\n', '\\n').replace('\r', '\\r')
        full_message = f"{message} | TRACEBACK: {traceback_escaped}"
    else:
        full_message = message

    # Format: [EVENT_TYPE] user_type | timestamp | username | message
    structured_log = f"[{event_type}] {user_type} | {timestamp} | {username} | {full_message}"

    if event_type == "FAIL":
        logger.error(structured_log)
    else:
        logger.info(structured_log)

# Create Flask app
app = Flask(__name__)

# Configure CORS to allow requests from any origin
cors = CORS(app, resources={
    r"/*": {
        "origins": "*",
        "methods": ["GET", "POST", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"],
        "supports_credentials": True,
        "expose_headers": ["Content-Disposition"]
    }
})

# Create uploads directory
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Create a directory for temporary files
TEMP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
os.makedirs(TEMP_FOLDER, exist_ok=True)

# Function to run async code in Flask
def run_async(coro):
    """Run an async coroutine in a synchronous context."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

def get_pdf_page_count(pdf_path):
    """
    Get the number of pages in a PDF file.

    Args:
        pdf_path: Path to the PDF file

    Returns:
        Number of pages in the PDF, or 0 if there's an error
    """
    try:
        reader = PdfReader(pdf_path)
        return len(reader.pages)
    except Exception as e:
        logger.error(f"Error getting PDF page count for {pdf_path}: {e}")
        return 0

@app.route('/api/process', methods=['POST'])
def process_pdf():
    """
    API endpoint to process a PDF file and return the Excel file.

    Expects a PDF file in the request along with processingType and processingName parameters.
    Returns the Excel file for download.
    """
    try:
        # Check if a file was uploaded
        if 'file' not in request.files:
            return jsonify({'error': 'No file part'}), 400

        file = request.files['file']

        # Check if the file is empty
        if file.filename == '':
            return jsonify({'error': 'No selected file'}), 400

        # Check if the file is a PDF
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({'error': 'File must be a PDF'}), 400

        # Get processing parameters from form data
        processing_type = request.form.get('processingType', 'Banks')  # Default to 'Banks'
        processing_name = request.form.get('processingName', 'Belgrade State Bank')  # Default value

        logger.info(f"Processing parameters - Type: {processing_type}, Name: {processing_name}")

        # Generate a unique ID for this upload
        upload_id = str(uuid.uuid4())

        # Create a directory for this upload
        upload_dir = os.path.join(UPLOAD_FOLDER, upload_id)
        os.makedirs(upload_dir, exist_ok=True)

        # Save the uploaded file
        pdf_path = os.path.join(upload_dir, file.filename)
        file.save(pdf_path)

        logger.info(f"File uploaded: {pdf_path}")

        # Log the start of processing
        log_structured_event("SUCCESS", f"File upload started: {file.filename}")

        # Process the PDF file
        start_time = time.time()
        logger.info(f"Starting bank statement processing for: {pdf_path}")

        if processing_type == "Banks":
            # Call the process_bank_statement function with processing parameters
            results_dir = run_async(process_bank_statement(pdf_path, processing_type, processing_name))

            if not results_dir:
                logger.error("Bank statement processing failed")
                return jsonify({'error': 'Processing failed'}), 500

            processing_time = time.time() - start_time
            logger.info(f"Bank statement processing completed in {processing_time:.2f} seconds")
            logger.info(f"Results saved to: {results_dir}")

            # Generate Excel report
            if process_json_and_generate_report:
                try:
                    excel_start_time = time.time()
                    logger.info(f"Generating Excel report for: {results_dir}")
                    excel_file_path = process_json_and_generate_report(results_dir)
                    excel_time = time.time() - excel_start_time
                    logger.info(f"Excel report generated successfully in {excel_time:.2f} seconds")

                    # Return the Excel file for download
                    response = send_file(
                        excel_file_path,
                        as_attachment=True,
                        download_name=f"{os.path.splitext(file.filename)[0]}.xlsx",
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )

                    # Clean up after sending the file
                    @after_this_request
                    def cleanup(response):
                        try:
                            # Clean up the upload directory after a delay
                            # This ensures the file is sent before cleanup
                            def delayed_cleanup():
                                time.sleep(5)  # Wait 5 seconds
                                shutil.rmtree(upload_dir, ignore_errors=True)

                            # Start cleanup in a separate thread
                            import threading
                            threading.Thread(target=delayed_cleanup).start()
                        except Exception as e:
                            logger.error(f"Error in cleanup: {e}")
                        return response

                    return response
                except Exception as e:
                    logger.error(f"Failed to generate Excel report: {e}")
                    logger.error(traceback.format_exc())
                    return jsonify({'error': 'Excel generation failed'}), 500
            else:
                return jsonify({'error': 'Excel generation module not available'}), 500
        
        else:
            # Call the process_credit_cards function with processing parameters
            results_dir = run_async(process_credit_cards(pdf_path, processing_type, processing_name))

            if not results_dir:
                logger.error("Credit card processing failed")
                return jsonify({'error': 'Processing failed'}), 500

            processing_time = time.time() - start_time
            logger.info(f"Credit card processing completed in {processing_time:.2f} seconds")
            logger.info(f"Results saved to: {results_dir}")

            # Generate Excel report
            if creditCardExcel:
                try:
                    excel_start_time = time.time()
                    logger.info(f"Generating Excel report for: {results_dir}")
                    excel_file_path = creditCardExcel(results_dir)
                    excel_time = time.time() - excel_start_time
                    logger.info(f"Excel report generated successfully in {excel_time:.2f} seconds")

                    # Log successful processing with structured format
                    log_structured_event(
                        "SUCCESS",
                        f"File processed successfully: {file.filename}, Type: {processing_type}, Name: {processing_name}, Processing time: {processing_time:.2f}s, Excel generation time: {excel_time:.2f}s"
                    )

                    # Return the Excel file for download
                    response = send_file(
                        excel_file_path,
                        as_attachment=True,
                        download_name=f"{os.path.splitext(file.filename)[0]}.xlsx",
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )

                    # Clean up after sending the file
                    @after_this_request
                    def cleanup(response):
                        try:
                            # Clean up the upload directory after a delay
                            # This ensures the file is sent before cleanup
                            def delayed_cleanup():
                                time.sleep(5)  # Wait 5 seconds
                                shutil.rmtree(upload_dir, ignore_errors=True)

                            # Start cleanup in a separate thread
                            import threading
                            threading.Thread(target=delayed_cleanup).start()
                        except Exception as e:
                            logger.error(f"Error in cleanup: {e}")
                        return response

                    return response
                except Exception as e:
                    logger.error(f"Failed to generate Excel report: {e}")
                    logger.error(traceback.format_exc())

                    # Log failure with structured format including traceback
                    log_structured_event(
                        "FAIL",
                        f"Excel generation failed for file: {file.filename}, Type: {processing_type}, Name: {processing_name}",
                        traceback_info=traceback.format_exc()
                    )

                    return jsonify({'error': 'Excel generation failed'}), 500
            else:
                return jsonify({'error': 'Excel generation module not available'}), 500

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        logger.error(traceback.format_exc())

        # Log general failure with structured format
        log_structured_event(
            "FAIL",
            f"PDF processing failed: {str(e)}",
            traceback_info=traceback.format_exc()
        )

        return jsonify({'error': str(e)}), 500

@app.route('/api/history', methods=['GET'])
def get_history():
    """
    API endpoint to get the system-wide processing history.

    Returns a list of all processed files with their details.
    """
    try:
        history = []
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")

        if not os.path.exists(data_dir):
            return jsonify({'history': []}), 200

        # List all timestamped directories in Data1
        for timestamp_dir in sorted(os.listdir(data_dir), reverse=True):
            timestamp_path = os.path.join(data_dir, timestamp_dir)

            # Skip if not a directory
            if not os.path.isdir(timestamp_path):
                continue

            # Check for input PDF
            input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
            if not os.path.exists(input_pdf_dir):
                continue

            # Get the PDF file name
            pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
            if not pdf_files:
                continue

            # Check if Excel file was generated
            excel_file_path = None
            output_dir = os.path.join(timestamp_path, "output")
            if os.path.exists(output_dir):
                excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
                if excel_files:
                    excel_file_path = os.path.join(output_dir, excel_files[0])

            # Get file size and page count
            pdf_path = os.path.join(input_pdf_dir, pdf_files[0])
            file_size = os.path.getsize(pdf_path)
            file_size_str = f"{file_size / 1024 / 1024:.2f} MB" if file_size > 1024 * 1024 else f"{file_size / 1024:.0f} KB"
            page_count = get_pdf_page_count(pdf_path)

            # Parse timestamp from directory name
            try:
                # Format: YYYYMMDD_HHMMSS_randomsuffix
                # Extract the datetime part (before the random suffix)
                timestamp_parts = timestamp_dir.split('_')
                if len(timestamp_parts) >= 2:
                    # Get the date and time parts
                    date_part = timestamp_parts[0]
                    time_part = timestamp_parts[1]

                    # Parse the date and time
                    year = int(date_part[0:4])
                    month = int(date_part[4:6])
                    day = int(date_part[6:8])
                    hour = int(time_part[0:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])

                    # Create ISO format timestamp
                    timestamp = f"{year:04d}-{month:02d}-{day:02d}T{hour:02d}:{minute:02d}:{second:02d}"
                else:
                    # If the format doesn't match, use the directory name as timestamp
                    timestamp = timestamp_dir
            except (ValueError, IndexError):
                # If parsing fails, use the directory name as timestamp
                timestamp = timestamp_dir

            # Add to history
            history.append({
                "id": timestamp_dir,
                "fileName": pdf_files[0],
                "uploadDate": timestamp,
                "fileSize": file_size_str,
                "pageCount": page_count,
                "excelPath": excel_file_path
            })

        return jsonify({'history': history}), 200

    except Exception as e:
        logger.error(f"Error getting history: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """
    API endpoint to check if the server is running.

    Returns:
        A simple JSON response indicating the server is running.
    """
    return jsonify({
        'status': 'ok',
        'message': 'API server is running',
        'timestamp': time.time()
    }), 200

# API endpoint to check if an Excel file is available
@app.route('/api/check-excel/<timestamp_id>', methods=['GET'])
def check_excel_availability(timestamp_id):
    """
    API endpoint to check if an Excel file is available for a given timestamp.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        JSON response with availability status and Excel file name
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID', 'available': False}), 400

        # Construct the path to the Excel file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        output_dir = os.path.join(timestamp_path, "output")

        logger.info(f"Checking Excel availability for timestamp: {timestamp_id}")
        logger.info(f"Looking for Excel file in: {output_dir}")

        if not os.path.exists(output_dir):
            logger.error(f"Output directory not found: {output_dir}")
            return jsonify({'error': f'Output directory not found for timestamp {timestamp_id}', 'available': False}), 200

        # Find the Excel file
        excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
        if not excel_files:
            logger.error(f"No Excel files found in directory: {output_dir}")
            return jsonify({'error': 'Excel file not found in the output directory', 'available': False, 'excelFileName': None}), 200

        excel_file_path = os.path.join(output_dir, excel_files[0])
        logger.info(f"Found Excel file: {excel_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(excel_file_path):
            logger.error(f"Excel file does not exist: {excel_file_path}")
            return jsonify({'error': 'Excel file does not exist', 'available': False, 'excelFileName': None}), 200

        # Get the original PDF filename
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        original_filename = pdf_files[0] if pdf_files else "processed_statement.pdf"
        logger.info(f"Original PDF filename: {original_filename}")

        # Return success response
        return jsonify({
            'available': True,
            'excelFileName': excel_files[0],
            'excelPath': excel_file_path,
            'originalFileName': original_filename
        }), 200

    except Exception as e:
        logger.error(f"Error checking Excel availability: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e), 'available': False, 'excelFileName': None}), 500


@app.route('/api/download/<timestamp_id>', methods=['GET'])
def download_excel(timestamp_id):
    """
    API endpoint to download an Excel file from the history.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        The Excel file for download
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID'}), 400

        # Construct the path to the Excel file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        output_dir = os.path.join(timestamp_path, "output")

        logger.info(f"Download request for timestamp: {timestamp_id}")
        logger.info(f"Looking for Excel file in: {output_dir}")

        if not os.path.exists(output_dir):
            logger.error(f"Output directory not found: {output_dir}")
            return jsonify({'error': f'Output directory not found for timestamp {timestamp_id}'}), 404

        # Find the Excel file
        excel_files = [f for f in os.listdir(output_dir) if f.lower().endswith('.xlsx')]
        if not excel_files:
            logger.error(f"No Excel files found in directory: {output_dir}")
            return jsonify({'error': 'Excel file not found in the output directory'}), 404

        excel_file_path = os.path.join(output_dir, excel_files[0])
        logger.info(f"Found Excel file: {excel_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(excel_file_path):
            logger.error(f"Excel file does not exist: {excel_file_path}")
            return jsonify({'error': 'Excel file does not exist'}), 404

        # Get the original PDF filename
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        original_filename = pdf_files[0] if pdf_files else "processed_statement.pdf"
        logger.info(f"Original PDF filename: {original_filename}")

        # Return the Excel file for download
        download_name = f"{os.path.splitext(original_filename)[0]}.xlsx"
        logger.info(f"Sending file for download: {download_name}")

        try:
            # Read the file into memory
            with open(excel_file_path, 'rb') as f:
                file_data = f.read()

            # Create a response with the file data
            from flask import Response
            response = Response(
                file_data,
                status=200,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

            # Set headers to force download
            response.headers["Content-Disposition"] = f"attachment; filename={download_name}"
            response.headers["Content-Length"] = len(file_data)
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Expose-Headers'] = 'Content-Disposition, Content-Length'
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

            logger.info(f"Successfully prepared file for download: {excel_file_path} with size {len(file_data)} bytes")
            return response
        except Exception as send_error:
            logger.error(f"Error sending file: {send_error}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error sending file: {str(send_error)}'}), 500

    except Exception as e:
        logger.error(f"Error downloading Excel: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/download-pdf/<timestamp_id>', methods=['GET'])
def download_pdf(timestamp_id):
    """
    API endpoint to download the original PDF file from the history.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        The PDF file for download
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID'}), 400

        # Construct the path to the PDF file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")

        logger.info(f"Download PDF request for timestamp: {timestamp_id}")
        logger.info(f"Looking for PDF file in: {input_pdf_dir}")

        if not os.path.exists(input_pdf_dir):
            logger.error(f"Input PDF directory not found: {input_pdf_dir}")
            return jsonify({'error': f'Input PDF directory not found for timestamp {timestamp_id}'}), 404

        # Find the PDF file
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        if not pdf_files:
            logger.error(f"No PDF files found in directory: {input_pdf_dir}")
            return jsonify({'error': 'PDF file not found in the inputPDF directory'}), 404

        pdf_file_path = os.path.join(input_pdf_dir, pdf_files[0])
        logger.info(f"Found PDF file: {pdf_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(pdf_file_path):
            logger.error(f"PDF file does not exist: {pdf_file_path}")
            return jsonify({'error': 'PDF file does not exist'}), 404

        # Return the PDF file for download
        download_name = pdf_files[0]
        logger.info(f"Sending PDF file for download: {download_name}")

        try:
            return send_file(
                pdf_file_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/pdf'
            )
        except Exception as send_error:
            logger.error(f"Error sending PDF file: {send_error}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error sending PDF file: {str(send_error)}'}), 500

    except Exception as e:
        logger.error(f"Error downloading PDF: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/view-pdf/<timestamp_id>', methods=['GET'])
def view_pdf(timestamp_id):
    """
    API endpoint to view the original PDF file in browser.

    Args:
        timestamp_id: The timestamp directory ID

    Returns:
        The PDF file for viewing in browser
    """
    try:
        # Validate the timestamp ID
        if not timestamp_id or '..' in timestamp_id:
            logger.error(f"Invalid timestamp ID: {timestamp_id}")
            return jsonify({'error': 'Invalid timestamp ID'}), 400

        # Construct the path to the PDF file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Data1")
        timestamp_path = os.path.join(data_dir, timestamp_id)
        input_pdf_dir = os.path.join(timestamp_path, "inputPDF")

        logger.info(f"View PDF request for timestamp: {timestamp_id}")
        logger.info(f"Looking for PDF file in: {input_pdf_dir}")

        if not os.path.exists(input_pdf_dir):
            logger.error(f"Input PDF directory not found: {input_pdf_dir}")
            return jsonify({'error': f'Input PDF directory not found for timestamp {timestamp_id}'}), 404

        # Find the PDF file
        pdf_files = [f for f in os.listdir(input_pdf_dir) if f.lower().endswith('.pdf')]
        if not pdf_files:
            logger.error(f"No PDF files found in directory: {input_pdf_dir}")
            return jsonify({'error': 'PDF file not found in the inputPDF directory'}), 404

        pdf_file_path = os.path.join(input_pdf_dir, pdf_files[0])
        logger.info(f"Found PDF file: {pdf_file_path}")

        # Verify the file exists and is readable
        if not os.path.isfile(pdf_file_path):
            logger.error(f"PDF file does not exist: {pdf_file_path}")
            return jsonify({'error': 'PDF file does not exist'}), 404

        # Return the PDF file for viewing in browser
        logger.info(f"Serving PDF file for viewing: {pdf_files[0]}")

        try:
            return send_file(
                pdf_file_path,
                as_attachment=False,  # This allows viewing in browser
                mimetype='application/pdf',
                conditional=True  # Enable conditional requests for better performance
            )
        except Exception as send_error:
            logger.error(f"Error serving PDF file: {send_error}")
            logger.error(traceback.format_exc())
            return jsonify({'error': f'Error serving PDF file: {str(send_error)}'}), 500

    except Exception as e:
        logger.error(f"Error viewing PDF: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/ui-listing', methods=['GET'])
def get_ui_listing():
    """
    API endpoint to get the UI listing configuration for Banks and Credit Cards.

    Returns the JSON configuration from config/frontendUIListing.json
    """
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'frontendUIListing.json')

        if not os.path.exists(config_path):
            logger.error(f"UI listing config file not found: {config_path}")
            return jsonify({'error': 'Configuration file not found'}), 404

        with open(config_path, 'r', encoding='utf-8') as f:
            ui_listing = json.load(f)

        logger.info("UI listing configuration retrieved successfully")
        return jsonify(ui_listing)

    except Exception as e:
        logger.error(f"Error reading UI listing configuration: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """
    API endpoint to get structured application logs (success/fail only).

    Query parameters:
        - start_date: Filter logs from this date (YYYY-MM-DD format)
        - end_date: Filter logs until this date (YYYY-MM-DD format)
        - status: Filter by status (SUCCESS, FAIL)
        - limit: Maximum number of log entries to return (default: 1000)

    Returns:
        JSON response with structured log entries
    """
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status_filter = request.args.get('status', '').upper()
        limit = int(request.args.get('limit', 1000))

        # Path to log file
        log_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs", "log")

        if not os.path.exists(log_file_path):
            logger.warning(f"Log file not found: {log_file_path}")
            return jsonify({'logs': [], 'total': 0}), 200

        logs = []

        # Read log file
        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        # Parse structured log entries
        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                # First check if this is a structured log with [SUCCESS] or [FAIL]
                if "[SUCCESS]" in line or "[FAIL]" in line:
                    # Parse structured log format: "[EVENT_TYPE] user_type | timestamp | username | message"
                    if "[SUCCESS]" in line:
                        status = "SUCCESS"
                        parts = line.split("[SUCCESS]", 1)[1].strip()
                    else:
                        status = "FAIL"
                        parts = line.split("[FAIL]", 1)[1].strip()

                    # Split the parts by pipe
                    try:
                        components = parts.split(" | ", 3)
                        if len(components) >= 3:
                            user_type = components[0].strip()
                            timestamp_str = components[1].strip()
                            username = components[2].strip()

                            # Message might contain traceback
                            message = components[3] if len(components) > 3 else ""

                            # Extract traceback if present
                            traceback_info = None
                            if "TRACEBACK:" in message:
                                message_parts = message.split("TRACEBACK:", 1)
                                message = message_parts[0].strip()
                                traceback_raw = message_parts[1].strip()

                                # Restore newlines from escaped format
                                traceback_info = traceback_raw.replace('\\n', '\n').replace('\\r', '\r')

                                # If traceback is empty or just contains the header, set to None
                                if not traceback_info or traceback_info.strip() == "Traceback (most recent call last):":
                                    traceback_info = None

                            # Parse timestamp
                            try:
                                timestamp = time.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                                log_date = time.strftime('%Y-%m-%d', timestamp)
                                log_time = time.strftime('%H:%M:%S', timestamp)

                                # Apply filters
                                if start_date and log_date < start_date:
                                    continue
                                if end_date and log_date > end_date:
                                    continue
                                if status_filter and status != status_filter:
                                    continue

                                # Add to logs
                                logs.append({
                                    'status': status,
                                    'user_type': user_type,
                                    'timestamp': timestamp_str,
                                    'date': log_date,
                                    'time': log_time,
                                    'username': username,
                                    'message': message,
                                    'traceback': traceback_info
                                })
                            except ValueError:
                                # If timestamp parsing fails, skip this entry
                                continue
                        else:
                            # Skip malformed structured logs
                            continue
                    except Exception as split_error:
                        # Skip parsing errors
                        continue
                else:
                    # Skip non-structured logs
                    continue

            except Exception as parse_error:
                logger.error(f"Error parsing log line: {parse_error}")
                continue

        # Sort logs by timestamp (newest first)
        logs.sort(key=lambda x: x['timestamp'], reverse=True)

        # Apply limit
        if limit > 0:
            logs = logs[:limit]

        logger.info(f"Retrieved {len(logs)} structured log entries")
        return jsonify({
            'logs': logs,
            'total': len(logs),
            'filters': {
                'start_date': start_date,
                'end_date': end_date,
                'status': status_filter,
                'limit': limit
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
