# scripts/utils.py

import json
import os
import logging
from ensure import ensure_annotations
from typing import Dict, Any, Union


 
def MloadConfig(strConfigPath: str = 'config/config.json') -> Dict[str, Any]:
    '''
    Purpose : Load configuration settings from a JSON file.

    Inputs  :   (1) strConfigPath : Path to the configuration JSON file (string)

    Output  : Returns a dictionary containing configuration settings or an empty dictionary in case of an error.

    Example : MloadConfig(strConfigPath='config/config.json')
    '''
    try:
        with open(strConfigPath, 'r') as objFile:
            return json.load(objFile)
    except Exception as e:
        logging.error(f"Failed to load config file {strConfigPath}: {e}")
        return {}


 
def MloadResponseFormat(strResponseFolderPath: str, strFileName: str) -> Union[Dict[str, Any], Dict]:
    '''
    Purpose : Load the response format from a JSON file.

    Inputs  :   (1) strResponseFolderPath : Path to the folder containing response format file (string)
               (2) strFileName : Name of the response format JSON file (string)

    Output  : Returns a dictionary containing the response format or an empty dictionary in case of an error.

    Example : MloadResponseFormat(strResponseFolderPath='resources/', strFileName='responseFormat.json')
    '''
    strFilePath: str = os.path.join(strResponseFolderPath, strFileName)
    try:
        with open(strFilePath, 'r') as objFile:
            return json.load(objFile)
    except Exception as e:
        logging.error(f"Failed to load response format file {strFilePath}: {e}")
        return {}


 
def MloadSystemPrompt(strResponseFolderPath: str, strFileName: str) -> str:
    '''
    Purpose : Load the system prompt from a text file.

    Inputs  :   (1) strResponseFolderPath : Path to the folder containing the system prompt file (string)
               (2) strFileName : Name of the system prompt text file (string)

    Output  : Returns the content of the system prompt file as a string or an empty string in case of an error.

    Example : MloadSystemPrompt(strResponseFolderPath='resources/', strFileName='strSystemPromptStructured.txt')
    '''
    strFilePath: str = os.path.join(strResponseFolderPath, strFileName)
    try:
        with open(strFilePath, 'r') as objFile:
            return objFile.read()
    except Exception as e:
        logging.error(f"Failed to load system prompt file {strFilePath}: {e}")
        return ""


 
def MsetupLogging(strLogFilePath: str) -> None:
    '''
    Purpose : Set up logging configuration.

    Inputs  :   (1) strLogFilePath : Path to the log file (string)

    Output  : None

    Example : MsetupLogging(strLogFilePath='logs/extractByOpenai.log')
    '''
    logger: logging.Logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    objHandler: logging.FileHandler = logging.FileHandler(strLogFilePath)
    strFormatter: str = '%(asctime)s - %(levelname)s - %(message)s'
    objFormatter: logging.Formatter = logging.Formatter(strFormatter)
    objHandler.setFormatter(objFormatter)
    if not logger.handlers:
        logger.addHandler(objHandler)
