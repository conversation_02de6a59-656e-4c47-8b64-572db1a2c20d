import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import { AccountBalance, CreditCard } from '@mui/icons-material';

interface UIListing {
  uiListing: {
    Banks: string[];
    'Credit Cards': string[];
  };
}

interface ProcessingTypeSelectorProps {
  onSelectionChange: (processingType: string, processingName: string) => void;
  disabled?: boolean;
}

const ProcessingTypeSelector: React.FC<ProcessingTypeSelectorProps> = ({ 
  onSelectionChange, 
  disabled = false 
}) => {
  const [processingType, setProcessingType] = useState<string>('Banks');
  const [processingName, setProcessingName] = useState<string>('');
  const [uiListing, setUiListing] = useState<UIListing | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch UI listing from backend
  useEffect(() => {
    const fetchUIListing = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://192.168.1.15:5000/api/ui-listing');
        
        if (!response.ok) {
          throw new Error(`Failed to fetch UI listing: ${response.status}`);
        }
        
        const data: UIListing = await response.json();
        setUiListing(data);
        
        // Set default processing name based on first item in Banks
        if (data.uiListing.Banks && data.uiListing.Banks.length > 0) {
          const defaultName = data.uiListing.Banks[0];
          setProcessingName(defaultName);
          onSelectionChange('Banks', defaultName);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching UI listing:', err);
        setError(err instanceof Error ? err.message : 'Failed to load options');
      } finally {
        setLoading(false);
      }
    };

    fetchUIListing();
  }, [onSelectionChange]);

  const handleProcessingTypeChange = (
    event: React.MouseEvent<HTMLElement>,
    newProcessingType: string | null,
  ) => {
    if (newProcessingType !== null && uiListing) {
      setProcessingType(newProcessingType);
      
      // Reset processing name and set to first item of the new type
      const options = uiListing.uiListing[newProcessingType as keyof typeof uiListing.uiListing];
      if (options && options.length > 0) {
        const firstOption = options[0];
        setProcessingName(firstOption);
        onSelectionChange(newProcessingType, firstOption);
      }
    }
  };

  const handleProcessingNameChange = (event: any) => {
    const newProcessingName = event.target.value;
    setProcessingName(newProcessingName);
    onSelectionChange(processingType, newProcessingName);
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Box display="flex" alignItems="center" justifyContent="center" gap={2}>
          <CircularProgress size={20} />
          <Typography variant="body2" color="text.secondary">
            Loading processing options...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Alert severity="error">
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
      </Paper>
    );
  }

  if (!uiListing) {
    return null;
  }

  const currentOptions = uiListing.uiListing[processingType as keyof typeof uiListing.uiListing] || [];

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
        Select Processing Type
      </Typography>
      
      {/* Processing Type Toggle */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
          Choose the type of financial document:
        </Typography>
        <ToggleButtonGroup
          value={processingType}
          exclusive
          onChange={handleProcessingTypeChange}
          disabled={disabled}
          sx={{
            '& .MuiToggleButton-root': {
              px: 3,
              py: 1,
              border: '1px solid',
              borderColor: 'divider',
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                }
              }
            }
          }}
        >
          <ToggleButton value="Banks" aria-label="banks">
            <AccountBalance sx={{ mr: 1 }} />
            Banks
          </ToggleButton>
          <ToggleButton value="Credit Cards" aria-label="credit cards">
            <CreditCard sx={{ mr: 1 }} />
            Credit Cards
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Processing Name Selector */}
      <FormControl fullWidth disabled={disabled}>
        <InputLabel id="processing-name-label">
          Select {processingType === 'Banks' ? 'Bank' : 'Credit Card'}
        </InputLabel>
        <Select
          labelId="processing-name-label"
          value={processingName}
          label={`Select ${processingType === 'Banks' ? 'Bank' : 'Credit Card'}`}
          onChange={handleProcessingNameChange}
          sx={{
            '& .MuiOutlinedInput-root': {
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'primary.main',
              }
            }
          }}
        >
          {currentOptions.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Current Selection Display */}
      <Box sx={{ mt: 2, p: 2, backgroundColor: 'action.hover', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          <strong>Selected:</strong> {processingType} → {processingName}
        </Typography>
      </Box>
    </Paper>
  );
};

export default ProcessingTypeSelector;
