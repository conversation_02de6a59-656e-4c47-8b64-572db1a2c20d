#!/usr/bin/env python3
"""
Service Health Check Test

This script tests if the frontend and backend services are running.
It's designed to be run as part of a Jenkins CI/CD pipeline.
It also sends email notifications about test results using Zoho SMTP.
"""

import unittest
import json
import os
import sys
import requests
import logging
import smtplib
import socket
import platform
import datetime
from email.message import EmailMessage
from requests.exceptions import RequestException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('service-health-check')

class ServiceHealthTest(unittest.TestCase):
    """Test case for checking service health."""

    @classmethod
    def setUpClass(cls):
        """Load configuration before running tests."""
        # Determine the path to the config file
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, 'config.json')

        # Load configuration
        try:
            with open(config_path, 'r') as f:
                cls.config = json.load(f)
            logger.info(f"Loaded configuration from {config_path}")
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

        # Set default values if not in config
        cls.frontend_url = cls.config.get('frontend', {}).get('url', 'http://************:5173')
        cls.frontend_timeout = cls.config.get('frontend', {}).get('timeout', 5)

        cls.backend_url = cls.config.get('backend', {}).get('url', 'http://************:5000')
        cls.backend_api_endpoint = cls.config.get('backend', {}).get('api_endpoint', '/api/health')
        cls.backend_timeout = cls.config.get('backend', {}).get('timeout', 5)

        logger.info(f"Frontend URL: {cls.frontend_url}")
        logger.info(f"Backend URL: {cls.backend_url}")

    def test_frontend_is_running(self):
        """Test if the frontend service is running."""
        try:
            logger.info(f"Testing frontend at {self.frontend_url}")
            response = requests.get(self.frontend_url, timeout=self.frontend_timeout)

            # Check if the response is successful (status code 200-299)
            self.assertTrue(
                200 <= response.status_code < 300,
                f"Frontend returned status code {response.status_code}"
            )
            logger.info("Frontend is running")
        except RequestException as e:
            logger.error(f"Frontend health check failed: {e}")
            self.fail(f"Frontend health check failed: {e}")

    def test_backend_is_running(self):
        """Test if the backend service is running."""
        try:
            backend_health_url = f"{self.backend_url}{self.backend_api_endpoint}"
            logger.info(f"Testing backend at {backend_health_url}")
            response = requests.get(backend_health_url, timeout=self.backend_timeout)

            # Check if the response is successful (status code 200-299)
            self.assertTrue(
                200 <= response.status_code < 300,
                f"Backend returned status code {response.status_code}"
            )

            # Check if the response contains the expected data
            response_data = response.json()
            self.assertEqual(response_data.get('status'), 'ok', "Backend status is not 'ok'")
            logger.info("Backend is running")
        except RequestException as e:
            logger.error(f"Backend health check failed: {e}")
            self.fail(f"Backend health check failed: {e}")

    @classmethod
    def send_email_notification(cls, test_results, success):
        """Send email notification about test results."""
        # Check if email notifications are enabled
        if not cls.config.get('email', {}).get('enabled', False):
            logger.info("Email notifications are disabled")
            return

        try:
            # Get email configuration
            email_config = cls.config.get('email', {})
            smtp_server = email_config.get('smtp_server')
            smtp_port = email_config.get('smtp_port')
            username = email_config.get('username')
            password = email_config.get('password')
            sender = email_config.get('sender')
            subject_prefix = email_config.get('subject_prefix', '[Service Health Check]')

            # Get the appropriate recipient list based on test result
            recipients_config = email_config.get('recipients', {})
            if isinstance(recipients_config, dict):
                # New format with separate lists for success and failure
                if success:
                    recipients = recipients_config.get('success', [])
                    logger.info("Using success recipients list")
                else:
                    recipients = recipients_config.get('failure', [])
                    logger.info("Using failure recipients list")
            else:
                # Fallback to old format with a single list
                recipients = recipients_config
                logger.info("Using common recipients list")

            if not all([smtp_server, smtp_port, username, password, sender]) or not recipients:
                logger.error("Incomplete email configuration or empty recipient list")
                return

            # Create message using EmailMessage
            msg = EmailMessage()
            msg['From'] = sender
            msg['To'] = ', '.join(recipients)

            # Set subject based on test result
            status = "SUCCESS" if success else "FAILURE"
            msg['Subject'] = f"{subject_prefix} {status}"

            # Build email body
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            system_info = f"Host: {socket.gethostname()}, Platform: {platform.platform()}"

            plain_body = f"""Service Health Check Results - {status}

Time: {current_time}
{system_info}

Test Results:
{test_results}
"""

            html_body = f"""<html>
<body>
<h2>Service Health Check Results - {status}</h2>
<p><b>Time:</b> {current_time}</p>
<p><b>System:</b> {system_info}</p>
<h3>Test Results:</h3>
<pre>{test_results}</pre>
</body>
</html>
"""

            # Set content like your working code
            msg.set_content(plain_body)
            msg.add_alternative(html_body, subtype='html')

            # Connect to SMTP server and send email
            try:
                # Try TLS connection first (most common)
                with smtplib.SMTP(smtp_server, smtp_port) as server:
                    server.starttls()
                    server.login(username, password)
                    server.send_message(msg)
            except Exception as e:
                logger.warning(f"TLS connection failed: {e}, trying SSL...")
                # Fall back to SSL connection
                with smtplib.SMTP_SSL(smtp_server, 465) as server:
                    server.login(username, password)
                    server.send_message(msg)

            logger.info(f"Email notification sent to {', '.join(recipients)}")
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP Authentication Failed: {e}")
            logger.info("Possible solutions:")
            logger.info("1. Verify your password is correct")
            logger.info("2. Generate an app-specific password in your Zoho account settings")
            logger.info("3. Check if 2FA is enabled and requires app password")
            logger.info("4. Ensure the sender email matches the username")
        except smtplib.SMTPException as e:
            logger.error(f"SMTP Error: {e}")
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            import traceback
            logger.error(traceback.format_exc())


class TestResultCollector(unittest.TextTestResult):
    """Custom TestResult class to collect test results for email notification."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.test_results = []

    def addSuccess(self, test):
        super().addSuccess(test)
        self.test_results.append(f"✅ {test.id()}: PASS")

    def addFailure(self, test, err):
        super().addFailure(test, err)
        self.test_results.append(f"❌ {test.id()}: FAIL - {err[1]}")

    def addError(self, test, err):
        super().addError(test, err)
        self.test_results.append(f"❌ {test.id()}: ERROR - {err[1]}")


class TestRunner(unittest.TextTestRunner):
    """Custom TestRunner to use our TestResultCollector."""

    def __init__(self, *args, **kwargs):
        kwargs['resultclass'] = TestResultCollector
        super().__init__(*args, **kwargs)

    def run(self, test):
        result = super().run(test)
        test_results = '\n'.join(result.test_results)
        success = result.wasSuccessful()

        # Send email notification
        if hasattr(test, 'test_case_class') and test.test_case_class == ServiceHealthTest:
            ServiceHealthTest.send_email_notification(test_results, success)

        return result


if __name__ == '__main__':
    # Create a test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(ServiceHealthTest)
    suite.test_case_class = ServiceHealthTest  # Add reference to test case class

    # Run tests with custom runner
    runner = TestRunner(verbosity=2)
    result = runner.run(suite)
