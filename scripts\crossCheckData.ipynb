{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "import json"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# creditPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\creditInfo\\febCredit_trueData.json\"\n", "# debitPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\debitInfo\\fabdebit_trueData.json\"\n", "# checkPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\checkNumberInorder\\febCheck_trueData.json\"\n", "# endPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\dailyEndingBalance\\febEnd_trueData.json\""]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# creditPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\creditInfo\\JanCredit_trueData.json\"\n", "# debitPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\debitInfo\\debitjan_trueData.json\"\n", "# checkPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\checkNumberInorder\\Jancheck_trueData.json\"\n", "# endPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\dailyEndingBalance\\jandailyEndingBalance_trueData.json\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# creditPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\creditInfo\\marchcredit_trueData.json\"\n", "# debitPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\debitInfo\\marchdebit_trueData.json\"\n", "# checkPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\checkNumberInorder\\marchCheck_trueData.json\"\n", "# endPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\dailyEndingBalance\\marchend_trueData.json\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["creditPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\creditInfo\\aprCredit_trueData.json\"\n", "debitPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\debitInfo\\aprdebit_trueData.json\"\n", "checkPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\checkNumberInorder\\aprCheck_trueData.json\"\n", "endPath = r\"Z:\\DEVELOPER_PUBLIC\\interns\\Satyam Tank\\USBankDetailExtraction\\data\\trueData\\dailyEndingBalance\\aprEnd_trueData.json\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["dfCredit = pd.json_normalize(pd.read_json(creditPath)[\"CreditsInfo\"])\n", "dfDebit = pd.json_normalize(pd.read_json(debitPath)[\"DebitInfo\"])\n", "dfCheck = pd.json_normalize(pd.read_json(checkPath)[\"CheckNumberinOrder\"])\n", "dfend = pd.json_normalize(pd.read_json(endPath)[\"dailyEndingBalance\"])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Load the JSON file\n", "with open(creditPath, \"r\") as f:\n", "    json_data = json.load(f)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Extract the year from 'StatementStartData'\n", "statement_start_date = json_data[\"StatementStartData\"]  # e.g., \"1/01/24\"\n", "year = \"20\" + statement_start_date.split(\"/\")[-1]  # Extract '24' and convert to '2024'"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["PreviousBalannce = float(json_data[\"Previous Balannce\"].replace(\",\", \"\"))  # Convert to float"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["314864.89"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["PreviousBalannce"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Convert to datetime using correct initial format\n", "dfCredit[\"Date\"] = pd.to_datetime(dfCredit[\"Date\"] + f\"/{year}\", format=\"%m/%d/%Y\")\n", "\n", "# Now, reformat to \"%d/%m/%Y\"\n", "dfCredit[\"Date\"] = dfCredit[\"Date\"].dt.strftime(\"%d/%m/%Y\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Convert to datetime using correct initial format\n", "dfDebit[\"Date\"] = pd.to_datetime(dfDebit[\"Date\"] + f\"/{year}\", format=\"%m/%d/%Y\")\n", "\n", "# Now, reformat to \"%d/%m/%Y\"\n", "dfDebit[\"Date\"] = dfDebit[\"Date\"].dt.strftime(\"%d/%m/%Y\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["\n", "# Convert to datetime using correct initial format\n", "dfCheck[\"Date\"] = pd.to_datetime(dfCheck[\"Date\"] + f\"/{year}\", format=\"%m/%d/%Y\")\n", "\n", "# Now, reformat to \"%d/%m/%Y\"\n", "dfCheck[\"Date\"] = dfCheck[\"Date\"].dt.strftime(\"%d/%m/%Y\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Convert to datetime using correct initial format\n", "dfend[\"Date\"] = pd.to_datetime(dfend[\"Date\"] + f\"/{year}\", format=\"%m/%d/%Y\")\n", "\n", "# Now, reformat to \"%d/%m/%Y\"\n", "dfend[\"Date\"] = dfend[\"Date\"].dt.strftime(\"%d/%m/%Y\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Description</th>\n", "      <th>Amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01/04/2024</td>\n", "      <td>MERCH SETL EPX ST 034671034 CCD</td>\n", "      <td>3913.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01/04/2024</td>\n", "      <td>MERCH SETL EPX ST 034671034 CCD</td>\n", "      <td>3786.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01/04/2024</td>\n", "      <td>MERCH SETL EPX ST 034671034 CCD</td>\n", "      <td>1126.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01/04/2024</td>\n", "      <td>Deposit/Credit</td>\n", "      <td>28687.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01/04/2024</td>\n", "      <td>Deposit/Credit</td>\n", "      <td>431.57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date                      Description    Amount\n", "0  01/04/2024  MERCH SETL EPX ST 034671034 CCD   3913.76\n", "1  01/04/2024  MERCH SETL EPX ST 034671034 CCD   3786.45\n", "2  01/04/2024  MERCH SETL EPX ST 034671034 CCD   1126.36\n", "3  01/04/2024                   Deposit/Credit  28687.68\n", "4  01/04/2024                   Deposit/Credit    431.57"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["dfCredit.head()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["creditDatewiseSum = dfCredit.groupby(\"Date\", as_index=False)[\"Amount\"].sum()\n", "debitDatewiseSum = dfDebit.groupby(\"Date\", as_index=False)[\"Amount\"].sum()\n", "checkDatewiseSum = dfCheck.groupby(\"Date\", as_index=False)[\"Amount\"].sum()\n", "endDatewiseSum = dfend.groupby(\"Date\", as_index=False)[\"Balance\"].sum()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01/04/2024</td>\n", "      <td>38030.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>02/04/2024</td>\n", "      <td>3843.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>03/04/2024</td>\n", "      <td>19246.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>04/04/2024</td>\n", "      <td>15373.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>05/04/2024</td>\n", "      <td>5015.61</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date    Amount\n", "0  01/04/2024  38030.82\n", "1  02/04/2024   3843.40\n", "2  03/04/2024  19246.69\n", "3  04/04/2024  15373.57\n", "4  05/04/2024   5015.61"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["creditDatewiseSum.head()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Ensure all Amounts are numeric\n", "dfCredit[\"Amount\"] = dfCredit[\"Amount\"].astype(float)\n", "dfDebit[\"Amount\"] = dfDebit[\"Amount\"].astype(float)\n", "dfCheck[\"Amount\"] = dfCheck[\"Amount\"].astype(float)\n", "dfend[\"Balance\"] = dfend[\"Balance\"].astype(float)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Rename columns before merging to ensure suffixes appear correctly\n", "checkDatewiseSum = checkDatewiseSum.rename(columns={\"Amount\": \"Amount_check\"})\n", "endDatewiseSum = endDatewiseSum.rename(columns={\"Balance\": \"Balance_end\"})"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Merge DataFrames\n", "merged_df = creditDatewiseSum.merge(debitDatewiseSum, on=\"Date\", how=\"outer\", suffixes=(\"_credit\", \"_debit\"))\n", "merged_df = merged_df.merge(checkDatewiseSum, on=\"Date\", how=\"outer\")\n", "merged_df = merged_df.merge(endDatewiseSum, on=\"Date\", how=\"outer\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Date', 'Amount_credit', 'Amount_debit', 'Amount_check', 'Balance_end'], dtype='object')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.columns"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Fill NaN values with 0, since missing values mean no transactions\n", "merged_df.fillna(0, inplace=True)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Amount_credit</th>\n", "      <th>Amount_debit</th>\n", "      <th>Amount_check</th>\n", "      <th>Balance_end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01/04/2024</td>\n", "      <td>38030.82</td>\n", "      <td>16448.45</td>\n", "      <td>16448.45</td>\n", "      <td>336447.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>02/04/2024</td>\n", "      <td>3843.40</td>\n", "      <td>3291.06</td>\n", "      <td>259.53</td>\n", "      <td>336999.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>03/04/2024</td>\n", "      <td>19246.69</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>356246.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>04/04/2024</td>\n", "      <td>15373.57</td>\n", "      <td>155.54</td>\n", "      <td>0.00</td>\n", "      <td>371464.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>05/04/2024</td>\n", "      <td>5015.61</td>\n", "      <td>59003.07</td>\n", "      <td>0.00</td>\n", "      <td>317476.86</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date  Amount_credit  Amount_debit  Amount_check  Balance_end\n", "0  01/04/2024       38030.82      16448.45      16448.45    336447.26\n", "1  02/04/2024        3843.40       3291.06        259.53    336999.60\n", "2  03/04/2024       19246.69          0.00          0.00    356246.29\n", "3  04/04/2024       15373.57        155.54          0.00    371464.32\n", "4  05/04/2024        5015.61      59003.07          0.00    317476.86"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df.head()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Sort by date to ensure proper sequential calculations\n", "merged_df.sort_values(by=\"Date\", inplace=True)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["CalculatedBalances = []\n", "for index, row in merged_df.iterrows():\n", "    # Formula: Previous Balance + Credit - Debit - Check\n", "    NewBalance = PreviousBalannce + row[\"Amount_credit\"] - row[\"Amount_debit\"] - row[\"Amount_check\"]\n", "    CalculatedBalances.append(NewBalance)\n", "    \n", "    # Update PreviousBalannce to the previous day's ending balance (from Balance_end column)\n", "    if index + 1 < len(merged_df):  # Ensure we don't go out of bounds\n", "        PreviousBalannce = merged_df.iloc[index + 1][\"Balance_end\"]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Add calculated balance to the DataFrame\n", "merged_df[\"CalculatedBalances\"] = CalculatedBalances"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Amount_credit</th>\n", "      <th>Amount_debit</th>\n", "      <th>Amount_check</th>\n", "      <th>Balance_end</th>\n", "      <th>CalculatedBalances</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01/04/2024</td>\n", "      <td>38030.82</td>\n", "      <td>16448.45</td>\n", "      <td>16448.45</td>\n", "      <td>336447.26</td>\n", "      <td>319998.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>02/04/2024</td>\n", "      <td>3843.40</td>\n", "      <td>3291.06</td>\n", "      <td>259.53</td>\n", "      <td>336999.60</td>\n", "      <td>337292.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>03/04/2024</td>\n", "      <td>19246.69</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>356246.29</td>\n", "      <td>375492.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>04/04/2024</td>\n", "      <td>15373.57</td>\n", "      <td>155.54</td>\n", "      <td>0.00</td>\n", "      <td>371464.32</td>\n", "      <td>386682.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>05/04/2024</td>\n", "      <td>5015.61</td>\n", "      <td>59003.07</td>\n", "      <td>0.00</td>\n", "      <td>317476.86</td>\n", "      <td>263489.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>08/04/2024</td>\n", "      <td>52754.63</td>\n", "      <td>17540.03</td>\n", "      <td>0.00</td>\n", "      <td>352691.46</td>\n", "      <td>387906.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>09/04/2024</td>\n", "      <td>6834.29</td>\n", "      <td>39297.82</td>\n", "      <td>0.00</td>\n", "      <td>320227.93</td>\n", "      <td>287764.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10/04/2024</td>\n", "      <td>20360.83</td>\n", "      <td>176685.34</td>\n", "      <td>0.00</td>\n", "      <td>163903.42</td>\n", "      <td>7578.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>11/04/2024</td>\n", "      <td>14690.35</td>\n", "      <td>70365.24</td>\n", "      <td>0.00</td>\n", "      <td>108228.53</td>\n", "      <td>52553.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>12/04/2024</td>\n", "      <td>10525.17</td>\n", "      <td>12123.50</td>\n", "      <td>0.00</td>\n", "      <td>106630.20</td>\n", "      <td>105031.87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>15/04/2024</td>\n", "      <td>61262.24</td>\n", "      <td>9867.23</td>\n", "      <td>0.00</td>\n", "      <td>158025.21</td>\n", "      <td>209420.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>16/04/2024</td>\n", "      <td>106425.77</td>\n", "      <td>11551.21</td>\n", "      <td>0.00</td>\n", "      <td>252899.77</td>\n", "      <td>347774.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>17/04/2024</td>\n", "      <td>66813.26</td>\n", "      <td>20364.85</td>\n", "      <td>0.00</td>\n", "      <td>299348.18</td>\n", "      <td>345796.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>18/04/2024</td>\n", "      <td>9917.84</td>\n", "      <td>17507.23</td>\n", "      <td>0.00</td>\n", "      <td>291758.79</td>\n", "      <td>284169.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>19/04/2024</td>\n", "      <td>8685.23</td>\n", "      <td>39.00</td>\n", "      <td>0.00</td>\n", "      <td>300405.02</td>\n", "      <td>309051.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>22/04/2024</td>\n", "      <td>70394.55</td>\n", "      <td>16798.37</td>\n", "      <td>0.00</td>\n", "      <td>354001.20</td>\n", "      <td>407597.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>23/04/2024</td>\n", "      <td>46473.38</td>\n", "      <td>8880.49</td>\n", "      <td>0.00</td>\n", "      <td>391594.09</td>\n", "      <td>429186.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>24/04/2024</td>\n", "      <td>17178.21</td>\n", "      <td>2925.77</td>\n", "      <td>0.00</td>\n", "      <td>405846.53</td>\n", "      <td>420098.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>25/04/2024</td>\n", "      <td>12337.17</td>\n", "      <td>13964.63</td>\n", "      <td>0.00</td>\n", "      <td>404219.07</td>\n", "      <td>402591.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>26/04/2024</td>\n", "      <td>16844.18</td>\n", "      <td>34510.41</td>\n", "      <td>0.00</td>\n", "      <td>386552.84</td>\n", "      <td>368886.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>29/04/2024</td>\n", "      <td>8420.11</td>\n", "      <td>30340.14</td>\n", "      <td>0.00</td>\n", "      <td>364632.81</td>\n", "      <td>342712.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>30/04/2024</td>\n", "      <td>26551.20</td>\n", "      <td>8266.00</td>\n", "      <td>0.00</td>\n", "      <td>382903.01</td>\n", "      <td>401188.21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Date  Amount_credit  Amount_debit  Amount_check  Balance_end  \\\n", "0   01/04/2024       38030.82      16448.45      16448.45    336447.26   \n", "1   02/04/2024        3843.40       3291.06        259.53    336999.60   \n", "2   03/04/2024       19246.69          0.00          0.00    356246.29   \n", "3   04/04/2024       15373.57        155.54          0.00    371464.32   \n", "4   05/04/2024        5015.61      59003.07          0.00    317476.86   \n", "5   08/04/2024       52754.63      17540.03          0.00    352691.46   \n", "6   09/04/2024        6834.29      39297.82          0.00    320227.93   \n", "7   10/04/2024       20360.83     176685.34          0.00    163903.42   \n", "8   11/04/2024       14690.35      70365.24          0.00    108228.53   \n", "9   12/04/2024       10525.17      12123.50          0.00    106630.20   \n", "10  15/04/2024       61262.24       9867.23          0.00    158025.21   \n", "11  16/04/2024      106425.77      11551.21          0.00    252899.77   \n", "12  17/04/2024       66813.26      20364.85          0.00    299348.18   \n", "13  18/04/2024        9917.84      17507.23          0.00    291758.79   \n", "14  19/04/2024        8685.23         39.00          0.00    300405.02   \n", "15  22/04/2024       70394.55      16798.37          0.00    354001.20   \n", "16  23/04/2024       46473.38       8880.49          0.00    391594.09   \n", "17  24/04/2024       17178.21       2925.77          0.00    405846.53   \n", "18  25/04/2024       12337.17      13964.63          0.00    404219.07   \n", "19  26/04/2024       16844.18      34510.41          0.00    386552.84   \n", "20  29/04/2024        8420.11      30340.14          0.00    364632.81   \n", "21  30/04/2024       26551.20       8266.00          0.00    382903.01   \n", "\n", "    CalculatedBalances  \n", "0            319998.81  \n", "1            337292.41  \n", "2            375492.98  \n", "3            386682.35  \n", "4            263489.40  \n", "5            387906.06  \n", "6            287764.40  \n", "7              7578.91  \n", "8             52553.64  \n", "9            105031.87  \n", "10           209420.22  \n", "11           347774.33  \n", "12           345796.59  \n", "13           284169.40  \n", "14           309051.25  \n", "15           407597.38  \n", "16           429186.98  \n", "17           420098.97  \n", "18           402591.61  \n", "19           368886.61  \n", "20           342712.78  \n", "21           401188.21  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# Compare with actual end balance\n", "merged_df[\"BalanceMatch\"] = (merged_df[\"Balance_end\"].round(2) == merged_df[\"CalculatedBalances\"].round(2))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Amount_credit</th>\n", "      <th>Amount_debit</th>\n", "      <th>Amount_check</th>\n", "      <th>Balance_end</th>\n", "      <th>CalculatedBalances</th>\n", "      <th>BalanceMatch</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01/04/2024</td>\n", "      <td>38030.82</td>\n", "      <td>16448.45</td>\n", "      <td>16448.45</td>\n", "      <td>336447.26</td>\n", "      <td>319998.81</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>02/04/2024</td>\n", "      <td>3843.40</td>\n", "      <td>3291.06</td>\n", "      <td>259.53</td>\n", "      <td>336999.60</td>\n", "      <td>337292.41</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>03/04/2024</td>\n", "      <td>19246.69</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>356246.29</td>\n", "      <td>375492.98</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>04/04/2024</td>\n", "      <td>15373.57</td>\n", "      <td>155.54</td>\n", "      <td>0.00</td>\n", "      <td>371464.32</td>\n", "      <td>386682.35</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>05/04/2024</td>\n", "      <td>5015.61</td>\n", "      <td>59003.07</td>\n", "      <td>0.00</td>\n", "      <td>317476.86</td>\n", "      <td>263489.40</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>08/04/2024</td>\n", "      <td>52754.63</td>\n", "      <td>17540.03</td>\n", "      <td>0.00</td>\n", "      <td>352691.46</td>\n", "      <td>387906.06</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>09/04/2024</td>\n", "      <td>6834.29</td>\n", "      <td>39297.82</td>\n", "      <td>0.00</td>\n", "      <td>320227.93</td>\n", "      <td>287764.40</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10/04/2024</td>\n", "      <td>20360.83</td>\n", "      <td>176685.34</td>\n", "      <td>0.00</td>\n", "      <td>163903.42</td>\n", "      <td>7578.91</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>11/04/2024</td>\n", "      <td>14690.35</td>\n", "      <td>70365.24</td>\n", "      <td>0.00</td>\n", "      <td>108228.53</td>\n", "      <td>52553.64</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>12/04/2024</td>\n", "      <td>10525.17</td>\n", "      <td>12123.50</td>\n", "      <td>0.00</td>\n", "      <td>106630.20</td>\n", "      <td>105031.87</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>15/04/2024</td>\n", "      <td>61262.24</td>\n", "      <td>9867.23</td>\n", "      <td>0.00</td>\n", "      <td>158025.21</td>\n", "      <td>209420.22</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>16/04/2024</td>\n", "      <td>106425.77</td>\n", "      <td>11551.21</td>\n", "      <td>0.00</td>\n", "      <td>252899.77</td>\n", "      <td>347774.33</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>17/04/2024</td>\n", "      <td>66813.26</td>\n", "      <td>20364.85</td>\n", "      <td>0.00</td>\n", "      <td>299348.18</td>\n", "      <td>345796.59</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>18/04/2024</td>\n", "      <td>9917.84</td>\n", "      <td>17507.23</td>\n", "      <td>0.00</td>\n", "      <td>291758.79</td>\n", "      <td>284169.40</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>19/04/2024</td>\n", "      <td>8685.23</td>\n", "      <td>39.00</td>\n", "      <td>0.00</td>\n", "      <td>300405.02</td>\n", "      <td>309051.25</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>22/04/2024</td>\n", "      <td>70394.55</td>\n", "      <td>16798.37</td>\n", "      <td>0.00</td>\n", "      <td>354001.20</td>\n", "      <td>407597.38</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>23/04/2024</td>\n", "      <td>46473.38</td>\n", "      <td>8880.49</td>\n", "      <td>0.00</td>\n", "      <td>391594.09</td>\n", "      <td>429186.98</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>24/04/2024</td>\n", "      <td>17178.21</td>\n", "      <td>2925.77</td>\n", "      <td>0.00</td>\n", "      <td>405846.53</td>\n", "      <td>420098.97</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>25/04/2024</td>\n", "      <td>12337.17</td>\n", "      <td>13964.63</td>\n", "      <td>0.00</td>\n", "      <td>404219.07</td>\n", "      <td>402591.61</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>26/04/2024</td>\n", "      <td>16844.18</td>\n", "      <td>34510.41</td>\n", "      <td>0.00</td>\n", "      <td>386552.84</td>\n", "      <td>368886.61</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>29/04/2024</td>\n", "      <td>8420.11</td>\n", "      <td>30340.14</td>\n", "      <td>0.00</td>\n", "      <td>364632.81</td>\n", "      <td>342712.78</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>30/04/2024</td>\n", "      <td>26551.20</td>\n", "      <td>8266.00</td>\n", "      <td>0.00</td>\n", "      <td>382903.01</td>\n", "      <td>401188.21</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Date  Amount_credit  Amount_debit  Amount_check  Balance_end  \\\n", "0   01/04/2024       38030.82      16448.45      16448.45    336447.26   \n", "1   02/04/2024        3843.40       3291.06        259.53    336999.60   \n", "2   03/04/2024       19246.69          0.00          0.00    356246.29   \n", "3   04/04/2024       15373.57        155.54          0.00    371464.32   \n", "4   05/04/2024        5015.61      59003.07          0.00    317476.86   \n", "5   08/04/2024       52754.63      17540.03          0.00    352691.46   \n", "6   09/04/2024        6834.29      39297.82          0.00    320227.93   \n", "7   10/04/2024       20360.83     176685.34          0.00    163903.42   \n", "8   11/04/2024       14690.35      70365.24          0.00    108228.53   \n", "9   12/04/2024       10525.17      12123.50          0.00    106630.20   \n", "10  15/04/2024       61262.24       9867.23          0.00    158025.21   \n", "11  16/04/2024      106425.77      11551.21          0.00    252899.77   \n", "12  17/04/2024       66813.26      20364.85          0.00    299348.18   \n", "13  18/04/2024        9917.84      17507.23          0.00    291758.79   \n", "14  19/04/2024        8685.23         39.00          0.00    300405.02   \n", "15  22/04/2024       70394.55      16798.37          0.00    354001.20   \n", "16  23/04/2024       46473.38       8880.49          0.00    391594.09   \n", "17  24/04/2024       17178.21       2925.77          0.00    405846.53   \n", "18  25/04/2024       12337.17      13964.63          0.00    404219.07   \n", "19  26/04/2024       16844.18      34510.41          0.00    386552.84   \n", "20  29/04/2024        8420.11      30340.14          0.00    364632.81   \n", "21  30/04/2024       26551.20       8266.00          0.00    382903.01   \n", "\n", "    CalculatedBalances  BalanceMatch  \n", "0            319998.81         False  \n", "1            337292.41         False  \n", "2            375492.98         False  \n", "3            386682.35         False  \n", "4            263489.40         False  \n", "5            387906.06         False  \n", "6            287764.40         False  \n", "7              7578.91         False  \n", "8             52553.64         False  \n", "9            105031.87         False  \n", "10           209420.22         False  \n", "11           347774.33         False  \n", "12           345796.59         False  \n", "13           284169.40         False  \n", "14           309051.25         False  \n", "15           407597.38         False  \n", "16           429186.98         False  \n", "17           420098.97         False  \n", "18           402591.61         False  \n", "19           368886.61         False  \n", "20           342712.78         False  \n", "21           401188.21         False  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the results\n", "merged_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ENVusBankDetailExtract", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}